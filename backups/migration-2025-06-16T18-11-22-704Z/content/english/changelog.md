---
title: "Changelog"
description: "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt dolore magna aliquyam erat, sed diam voluptua. At vero eos et ustoLorem ipsum dolor sit amet, consetetur."
draft: false
---

### February Updates

**Feb 6, 2019**

Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt dolore magna aliquyam erat, sed diam voluptua. At vero eos et ustoLorem ipsum dolor sit amet, consetetur."

{{< changelog "changed" >}}
* Better support for using applying additional filters to posts_tax_query for categories for custom WordPress syncs

* Reporting fine-tuning for speed improvements (up to 60% improvement in latency)

* Replaced login / registration pre-app screens with a cleaner design
{{</ changelog >}}


{{< changelog "removed" >}}
* Removed an issue with the sync autolinker only interlinking selectively.
* Removed up an issue with prematurely logging out users
{{</ changelog >}}

{{< changelog "security" >}}
1. Removed an issue with the sync autolinker only interlinking selectively.
2. Removed up an issue with prematurely logging out users
{{</ changelog >}}

<hr>


### March Updates

**Mar 6, 2019**

Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor <br> invidunt dolore magna aliquyam erat, sed diam voluptua. At vero eos et ustoLorem ipsum dolor sit amet, consetetur."

{{< changelog "added" >}}
* Some scheduled changelogs, tweets, and slack messages queued up this weekend and were not published on time. We fixed the issue and all delayed publications should be out.
* We now prioritize keywords over title and body so customers can more effectively influence search results
* Support form in the Assistant is now protected with reCaptcha to reduce spam reinitializeOnUrlChange added to the JavaScript API to improve support for pages with turbolinks
{{</ changelog >}}


{{< changelog "fixed" >}}
* Fixed an issue with the sync autolinker only interlinking selectively.
* Fixed up an issue with prematurely logging out users
{{</ changelog >}}

<hr>

### Changelog label

{{< changelog "Added" >}}
{{</ changelog >}}

{{< changelog "Changed" >}}
{{</ changelog >}}

{{< changelog "Depricated" >}}
{{</ changelog >}}

{{< changelog "Removed" >}}
{{</ changelog >}}

{{< changelog "Fixed" >}}
{{</ changelog >}}

{{< changelog "Security" >}}
{{</ changelog >}}

{{< changelog "Unreleased" >}}
{{</ changelog >}}