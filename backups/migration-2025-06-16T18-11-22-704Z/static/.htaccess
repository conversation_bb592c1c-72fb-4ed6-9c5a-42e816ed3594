##### Optimize default expiration time - BEGIN
<IfModule mod_expires.c>

  "content_security_policy": "script-src 'self' https://apis.google.com/js/api.js 'unsafe-eval'; object-src 'self'"
  
  ## Enable expiration control
  ExpiresActive On

  ## CSS and JS expiration: 1 week after request
  ExpiresByType text/css "now plus 1 week"
  ExpiresByType application/javascript "now plus 1 week"
  ExpiresByType application/x-javascript "now plus 1 week"

  ## Image files expiration: 1 month after request
  ExpiresByType image/bmp "now plus 1 month"
  ExpiresByType image/gif "now plus 1 month"
  ExpiresByType image/jpeg "now plus 1 month"
  ExpiresByType image/webp "now plus 1 month"
  ExpiresByType image/jp2 "now plus 1 month"
  ExpiresByType image/pipeg "now plus 1 month"
  ExpiresByType image/png "now plus 1 month"
  ExpiresByType image/svg+xml "now plus 1 month"
  ExpiresByType image/tiff "now plus 1 month"
  ExpiresByType image/x-icon "now plus 1 month"
  ExpiresByType image/ico "now plus 1 month"
  ExpiresByType image/icon "now plus 1 month"
  ExpiresByType text/ico "now plus 1 month"
  ExpiresByType application/ico "now plus 1 month"
  ExpiresByType image/vnd.wap.wbmp "now plus 1 month"

  ## Font files expiration: 1 month after request
  ExpiresByType application/x-font-ttf "now plus 1 month"
  ExpiresByType application/x-font-opentype "now plus 1 month"
  ExpiresByType application/x-font-woff "now plus 1 month"
  ExpiresByType font/woff2 "now plus 1 month"
  ExpiresByType image/svg+xml "now plus 1 month"

  ## Audio files expiration: 1 month after request
  ExpiresByType audio/ogg "now plus 1 month"
  ExpiresByType application/ogg "now plus 1 month"
  ExpiresByType audio/basic "now plus 1 month"
  ExpiresByType audio/mid "now plus 1 month"
  ExpiresByType audio/midi "now plus 1 month"
  ExpiresByType audio/mpeg "now plus 1 month"
  ExpiresByType audio/mp3 "now plus 1 month"
  ExpiresByType audio/x-aiff "now plus 1 month"
  ExpiresByType audio/x-mpegurl "now plus 1 month"
  ExpiresByType audio/x-pn-realaudio "now plus 1 month"
  ExpiresByType audio/x-wav "now plus 1 month"

  ## Movie files expiration: 1 month after request
  ExpiresByType application/x-shockwave-flash "now plus 1 month"
  ExpiresByType x-world/x-vrml "now plus 1 month"
  ExpiresByType video/x-msvideo "now plus 1 month"
  ExpiresByType video/mpeg "now plus 1 month"
  ExpiresByType video/mp4 "now plus 1 month"
  ExpiresByType video/quicktime "now plus 1 month"
  ExpiresByType video/x-la-asf "now plus 1 month"
  ExpiresByType video/x-ms-asf "now plus 1 month"
</IfModule>
##### Optimize default expiration time - END

##### 1 Month for most static resources
<filesMatch ".(css|jpg|jpeg|png|webp|gif|js|ico|woff|woff2|eot|ttf)$">
  Header set Cache-Control "max-age=2592000, public"
</filesMatch>

##### Enable gzip compression for resources
<ifModule mod_gzip.c>
  mod_gzip_on Yes
  mod_gzip_dechunk Yes
  mod_gzip_item_include file .(html?|txt|css|js|php)$
  mod_gzip_item_include handler ^cgi-script$
  mod_gzip_item_include mime ^text/.*
  mod_gzip_item_include mime ^application/x-javascript.*
  mod_gzip_item_exclude mime ^image/.*
  mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</ifModule>

##### Or, compress certain file types by extension:
<FilesMatch *.(html|css|jpg|jpeg|webp|png|gif|js|ico)>
  SetOutputFilter DEFLATE
</FilesMatch>

##### Set Header Vary: Accept-Encoding
<IfModule mod_headers.c>
  <FilesMatch ".(js|css|xml|gz|html)$">
    Header append Vary: Accept-Encoding
  </FilesMatch>
</IfModule>