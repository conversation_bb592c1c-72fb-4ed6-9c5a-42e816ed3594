/* Hero section overlay for better text readability */
.hero-area {
	position: relative;
}

.hero-area::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
	z-index: 1;
}

.hero-area .container {
	position: relative;
	z-index: 2;
}

.hero-area h1, 
.hero-area p {
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Footer styling */
.footer {
    background-color: #000000;
    color: #ffffff;
    margin-top: 140px;
    padding-top: 140px;
}

.footer a {
    color: #ffffff;
}

.footer a:hover {
    color: #ff3130;
}

.copyright {
    background-color: #000000;
    color: #ffffff;
}

.copyright a {
    color: #ffffff;
}

.copyright a:hover {
    color: #ff3130;
}