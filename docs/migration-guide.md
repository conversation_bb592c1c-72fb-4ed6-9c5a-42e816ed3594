# Content Migration Guide

This guide explains how to migrate existing Hugo content to PocketBase collections using the automated migration scripts.

## Overview

The migration process transfers content from <PERSON>'s file-based structure to PocketBase's database collections, including:

- Site settings from `config/_default/params.toml`
- Navigation menus from `config/_default/menus.*.toml`
- Pages from `content/english/` and `content/french/`
- Team members from leadership sections in about.md
- Static assets (images, files)

## Prerequisites

1. **Deno Runtime**: Install Deno v1.40+ from [deno.land](https://deno.land)
2. **PocketBase Server**: Running instance with admin access
3. **Hugo Content**: Existing Hugo site structure
4. **Environment Variables**: Configured PocketBase credentials

## Setup

### 1. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp scripts/migration/.env.example scripts/migration/.env
```

Edit `.env` with your PocketBase credentials:

```env
POCKETBASE_URL=http://localhost:8090
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your_secure_password
```

### 2. Verify Content Structure

Ensure your Hugo site has the expected structure:

```
project/
├── content/
│   ├── english/
│   │   ├── _index.md
│   │   ├── about.md
│   │   └── ...
│   └── french/
│       ├── _index.md
│       ├── about.md
│       └── ...
├── config/
│   └── _default/
│       ├── params.toml
│       ├── menus.en.toml
│       └── menus.fr.toml
└── static/
    └── images/
        └── ...
```

## Migration Process

### Step 1: Content Analysis

Analyze your existing content structure:

```bash
cd scripts/migration
deno run --allow-read --allow-write analyze-content.ts
```

This generates a `migration-report-YYYY-MM-DD.json` file with content inventory.

### Step 2: Individual Migrations

Run individual migration scripts to test specific components:

```bash
# Migrate site settings
deno run --allow-read --allow-write --allow-net --allow-env migrate-site-settings.ts

# Migrate navigation menus
deno run --allow-read --allow-write --allow-net --allow-env migrate-menus.ts

# Migrate pages
deno run --allow-read --allow-write --allow-net --allow-env migrate-pages.ts

# Migrate team members
deno run --allow-read --allow-write --allow-net --allow-env migrate-team-members.ts
```

### Step 3: Complete Migration

Run the full migration process:

```bash
deno run --allow-read --allow-write --allow-net --allow-env migrate-all.ts
```

This script:
1. Validates content structure
2. Creates backup of existing content
3. Runs all migration scripts in sequence
4. Validates migration results
5. Generates comprehensive report

### Step 4: Validation

Verify migration success:

```bash
deno run --allow-read --allow-write --allow-net --allow-env validate-migration.ts
```

## Content Mapping

### Site Settings

| Hugo (params.toml) | PocketBase (site_settings) |
|-------------------|---------------------------|
| `description` | `company_description` |
| `email` | `contact_email` |
| `mobile` | `contact_phone` |
| `address` | `contact_address` |
| `logo` | `logo` (file upload) |
| `favicon` | `favicon` (file upload) |

### Navigation Menus

| Hugo (menus.*.toml) | PocketBase (navigation_menus) |
|--------------------|------------------------------|
| `name` | `label` |
| `URL` | `url` |
| `weight` | `weight` |
| Menu type (main/footer) | `menu_name` |
| File language | `language` |

### Pages

| Hugo (markdown files) | PocketBase (pages) |
|----------------------|-------------------|
| `title` | `title` |
| `description` | `meta_description` |
| `page_header_image` | `featured_image` |
| `watermark` | `watermark` |
| File content | `content` |
| File path | `slug` |

### Team Members

| Hugo (about.md) | PocketBase (team_members) |
|----------------|--------------------------|
| `name` | `name` |
| `position` | `position` |
| `image` | `photo` (file upload) |
| `bio` | `bio` |

## Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   # Ensure Deno has required permissions
   deno run --allow-all migrate-all.ts
   ```

2. **PocketBase Connection Failed**
   - Verify PocketBase server is running
   - Check URL and credentials in `.env`
   - Ensure admin account exists

3. **File Upload Errors**
   - Check file paths in Hugo content
   - Verify static assets exist
   - Ensure PocketBase has write permissions

4. **Content Structure Issues**
   - Run content analysis first
   - Check Hugo frontmatter format
   - Verify required directories exist

### Recovery

If migration fails:

1. **Restore from Backup**
   ```bash
   # Backups are created in ./backups/migration-TIMESTAMP/
   ls -la backups/
   ```

2. **Clear PocketBase Collections**
   - Use PocketBase admin UI
   - Or run individual migration scripts with `--clear` flag

3. **Retry Migration**
   ```bash
   # Fix issues and retry
   deno run --allow-all migrate-all.ts
   ```

## Validation

The migration includes comprehensive validation:

- **Data Integrity**: No duplicate IDs or missing required fields
- **Relationship Consistency**: Valid references between collections
- **Content Completeness**: All expected content migrated
- **File Uploads**: Assets properly uploaded and referenced

## Post-Migration

After successful migration:

1. **Test PocketBase Admin UI**: Verify all content appears correctly
2. **Test Content Sync**: Ensure Hugo sync framework works
3. **Backup PocketBase**: Create database backup
4. **Update Documentation**: Record any customizations made

## Support

For issues or questions:

1. Check migration logs and reports
2. Review validation output
3. Consult PocketBase documentation
4. Check Hugo content structure

## Migration Scripts Reference

- `analyze-content.ts` - Content structure analysis
- `migrate-site-settings.ts` - Site configuration migration
- `migrate-menus.ts` - Navigation menus migration
- `migrate-pages.ts` - Page content migration
- `migrate-team-members.ts` - Team member data migration
- `migrate-all.ts` - Complete migration orchestrator
- `validate-migration.ts` - Post-migration validation
- `utils.ts` - Shared utilities and helpers
