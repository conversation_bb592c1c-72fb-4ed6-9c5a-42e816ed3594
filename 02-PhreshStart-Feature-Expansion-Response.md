# PhreshStart Feature Expansion Response

## Table of Contents
- [File System Architecture](#file-system-architecture)
- [Feature Specifications](#feature-specifications)
  - [Feature 1: User Authentication & Profile Management](#feature-1-user-authentication--profile-management)
  - [Feature 2: Location Detection & Seasonal Content Engine](#feature-2-location-detection--seasonal-content-engine)
  - [Feature 3: Recipe Management System](#feature-3-recipe-management-system)
  - [Feature 4: Interactive Seasonal Produce Guide](#feature-4-interactive-seasonal-produce-guide)
  - [Feature 5: Content Management & Blog System](#feature-5-content-management--blog-system)
- [Future Features (Post-MVP)](#future-features-post-mvp)
  - [Feature 6: Premium Subscription System](#feature-6-premium-subscription-system)
  - [Feature 7: Expert Consultation Platform](#feature-7-expert-consultation-platform)
  - [Feature 8: Advanced Analytics Dashboard](#feature-8-advanced-analytics-dashboard)

---

## File System Architecture

This structure separates concerns between the client-facing application and the backend services, ensuring clarity and maintainability.

### Frontend (Astro.js Repository)
```
/
├── src/
│   ├── components/
│   │   ├── auth/
│   │   │   ├── LoginForm.astro
│   │   │   ├── RegistrationForm.astro
│   │   │   └── SocialAuthButtons.astro
│   │   ├── core/
│   │   │   ├── Header.astro
│   │   │   ├── Footer.astro
│   │   │   ├── Navigation.astro
│   │   │   └── InSeasonBadge.astro
│   │   ├── profile/
│   │   │   ├── ProfileForm.astro
│   │   │   ├── PreferencesManager.astro
│   │   │   └── SubscriptionManager.astro    # New
│   │   ├── recipes/
│   │   │   ├── RecipeCard.astro
│   │   │   ├── RecipeDetail.astro
│   │   │   ├── RecipeList.astro
│   │   │   └── IngredientSubstitutions.astro
│   │   ├── seasonal-guide/
│   │   │   ├── InteractiveCalendar.tsx       # React component for interactivity
│   │   │   └── ProduceDetailCard.astro
│   │   ├── premium/                         # New
│   │   │   ├── PricingTable.astro
│   │   │   ├── PremiumContentLock.astro
│   │   │   ├── WeeklyPlan.astro
│   │   │   └── ShoppingList.astro
│   │   ├── experts/                         # New
│   │   │   ├── ExpertProfile.astro
│   │   │   ├── BookingCalendar.tsx
│   │   │   └── QandAThread.astro
│   │   └── ui/
│   │       ├── Button.astro
│   │       ├── Input.astro
│   │       ├── Modal.astro
│   │       └── Spinner.astro
│   ├── layouts/
│   │   ├── MainLayout.astro
│   │   ├── ProfileLayout.astro
│   │   └── PremiumLayout.astro              # New
│   ├── pages/
│   │   ├── index.astro                     # Homepage with seasonal recipes
│   │   ├── auth/
│   │   │   ├── login.astro
│   │   │   ├── register.astro
│   │   │   └── reset-password.astro
│   │   ├── profile/
│   │   │   └── index.astro
│   │   ├── recipes/
│   │   │   ├── index.astro                 # Browse all recipes
│   │   │   └── [slug].astro                # Individual recipe page
│   │   ├── seasonal-guide.astro
│   │   ├── blog/
│   │   │   ├── index.astro
│   │   │   └── [slug].astro
│   │   ├── premium/                         # New
│   │   │   ├── index.astro
│   │   │   └── plans.astro
│   │   └── experts/                         # New
│   │       ├── index.astro
│   │       └── [id].astro
│   ├── services/
│   │   ├── api.ts                          # Central API client (Axios/Fetch wrapper)
│   │   ├── pocketbase.ts                   # PocketBase SDK helper
│   │   ├── location.ts                     # Geolocation service logic
│   │   └── analytics.ts                    # New: PostHog wrapper
│   ├── store/
│   │   ├── user.ts                         # State management for user auth/profile
│   │   └── location.ts                     # State for user's location
│   └── styles/
│       └── global.css
├── public/
│   ├── images/
│   └── fonts/
├── astro.config.mjs
└── package.json
```

### Backend (Go-extended PocketBase Repository)

```
/
├── pb_migrations/                  # PocketBase managed migrations
├── pb_public/                      # Static assets served by PocketBase
├── main.go                         # Main application entry point
├── go.mod
├── go.sum
├── hooks/
│   ├── mail.go                     # Hooks for sending emails (welcome, reset)
│   ├── recipe.go                   # Hooks for recipe data enrichment
│   └── user.go                     # Hooks for custom user logic on creation
├── data/
│   ├── seed/
│   │   └── seed_seasonal_data.go   # Script to seed PostgreSQL from CSV/JSON
│   └── pb_data/                    # The SQLite database file
├── db/
│   └── postgres.go                 # Connection and query logic for PostgreSQL
├── internal/
│   ├── services/
│   │   ├── nutritional_api.go      # Client for external nutritional API
│   │   ├── pdf_generator.go        # Service for generating lead magnet PDFs
│   │   ├── plan_generator.go       # New: Generates weekly juice plans
│   │   ├── payment_handler.go      # New: Logic for Paystack
│   │   └── analytics.go            # New: Server-side PostHog tracking
│   └── models/
│       └── produce.go              # Go structs for PostgreSQL data
├── routes/
│   ├── seasonal.go                 # Custom route for seasonal produce data
│   ├── lead_magnet.go              # Custom route for generating/downloading PDFs
│   ├── payments.go                 # New: Paystack checkout and webhooks
│   └── premium.go                  # New: Premium content-related endpoints
└── Dockerfile
```

---

## Feature Specifications

### Feature 1: User Authentication & Profile Management

**Feature Goal:** Provide a secure and seamless way for users to create accounts, manage their profiles, and persist their preferences to enable personalized experiences.

**API Relationships:** Directly utilizes PocketBase's built-in Authentication API. Integrates with an external email service (e.g., SendGrid, Mailgun) via backend hooks for transactional emails.

#### Detailed Feature Requirements

**System Architecture Overview:**

- Leverages PocketBase's native users collection for authentication and basic profile data
- Authentication logic is handled by the PocketBase SDK on the frontend and validated by the backend
- A custom Go hook in the backend will trigger on user creation to send a welcome email
- User preferences are stored in a separate user_profiles collection linked to the users collection

**Database Schema Design:**

*PocketBase Collection: `users` (Built-in)*
- Standard fields: `id`, `email`, `verified`, `name`, etc.

*PocketBase Collection: `user_profiles`*
- `id` (Primary Key)
- `user` (Relation to users, unique, required)
- `display_name` (Text, optional)
- `location_manual_override` (Text, optional, e.g., "New York, USA")
- `wellness_goals` (JSON, e.g., ["weight_loss", "energy_boost"])
- `dietary_preferences` (JSON, e.g., ["vegan", "nut_allergy"])
- `created` (Timestamp)
- `updated` (Timestamp)

**Comprehensive API Design (Leveraging PocketBase SDK):**

*Authentication:*
- `POST /api/collections/users/auth-with-password`: Standard email/password login
- `POST /api/collections/users/auth-with-oauth2`: Handles Google/Apple OAuth2 flow
- `POST /api/collections/users/request-password-reset`: Initiates password reset flow

*User Profile CRUD:*
- `POST /api/collections/user_profiles/records`: Create a user profile (Triggered by a backend hook on new user registration)
- `GET /api/collections/user_profiles/records?filter=(user.id='{userId}')`: Fetch the current user's profile
- `PATCH /api/collections/user_profiles/records/{recordId}`: Update the user's profile

*Authorization:* All user_profiles endpoints will be protected by PocketBase rules, ensuring a user can only read/write their own profile record. Rule: `user.id = @request.auth.id`.

**Frontend Architecture:**

- A global state store (`store/user.ts`) will manage the user's authentication state, token, and profile data across the application
- The `MainLayout.astro` will check this store to conditionally render UI elements (e.g., "Login" vs. "Profile")
- Components in `src/components/auth` will handle the forms and logic for login, registration, and social auth
- `src/pages/profile/index.astro` will be the main page for viewing and editing profile settings, using the `ProfileForm.astro` component

**Detailed CRUD Operations:**

- **Create:** A user signs up via email/password or OAuth. On successful creation in the users collection, a backend Go hook is triggered. This hook creates a corresponding record in user_profiles, linking it to the new user ID. A welcome email is also dispatched.

- **Read:** When a user logs in, the frontend fetches their users record and their user_profiles record and stores this information in the global state.

- **Update:** The user navigates to their profile page. The ProfileForm.astro component is pre-filled with data from the state. On submit, a PATCH request is sent to update their user_profiles record.

- **Delete:** User account deletion will be a two-step process initiated from the profile page. It will perform a hard delete on both the user_profiles record and the users record. A confirmation modal (ui/Modal.astro) must be displayed first.

**User Experience Flow:**

- **Registration:** User fills form → Clicks "Sign Up" → Frontend shows a loading spinner → On success, redirect to homepage with a "Welcome!" toast notification and update auth state → On failure, display specific error message below the form field (e.g., "Email already in use")

- **Profile Update:** User changes a preference → Clicks "Save" → Button shows a spinner → On success, button returns to normal and a "Profile Updated" toast appears → On failure, a generic "Could not save settings" error is shown

**Security Considerations:**

- All password handling is managed by PocketBase, which provides secure hashing
- Frontend will enforce strong password requirements visually
- OAuth2 implementation will strictly follow the provider's guidelines for handling tokens and redirects
- All user input for profiles will be sanitized on the backend by PocketBase before database insertion

**Testing Strategy:**

- **Unit Tests:** Test the state management logic in `store/user.ts`
- **Integration Tests:** Test the backend hooks to ensure a user_profiles record is created and an email is dispatched on user creation
- **E2E Tests:** Simulate a full user journey: registration, login, profile update, logout, password reset

**Data Management:** User profile data is fetched once upon login and cached in the frontend state. It should be re-fetched if the user navigates back to the profile page after a period of inactivity to ensure data freshness.

**Error Handling & Logging:**

- Frontend will display user-friendly errors (e.g., "Invalid credentials")
- Backend (PocketBase) will log authentication failures and successes with structured logs (user ID, timestamp, IP address). Critical failures like the email service being down will trigger high-priority alerts

---

### Feature 2: Location Detection & Seasonal Content Engine

**Feature Goal:** To automatically determine a user's geographical location to provide hyper-relevant recipe content based on locally available, in-season produce.

**API Relationships:**

- **Frontend → Geolocation API:** Uses a client-side IP geolocation service (e.g., ip-api.com) as the primary detection method
- **Frontend → Backend API:** Sends the detected location to a custom backend endpoint to retrieve a list of in-season produce
- **Backend → PostgreSQL:** The custom backend endpoint queries the dedicated PostgreSQL database for seasonal data
- **Backend → Redis:** The results from the PostgreSQL query are cached in Redis to reduce database load and improve response times

#### Detailed Feature Requirements

**System Architecture Overview:**

This is a multi-step process:

1. **Client-Side:** The Astro frontend attempts to get the user's location. The priority is: Manual Override (from User Profile) > IP Geolocation
2. **API Call:** The frontend sends the determined country and region to a custom Go endpoint running alongside PocketBase
3. **Backend Logic:** The Go endpoint first checks Redis for a cached result for that location. If a cache miss, it queries the PostgreSQL seasonal_produce table
4. **Caching:** The result from PostgreSQL is stored in Redis with a TTL (e.g., 24 hours)
5. **Response:** The list of in-season produce IDs is returned to the frontend
6. **Final Query:** The frontend uses this list of produce IDs to make a final filtered query to the PocketBase recipes collection

**Database Schema Design:**

*PostgreSQL Table: `seasonal_produce`*
- `id` (Serial, Primary Key)
- `produce_name` (Varchar, Not Null) - e.g., "Strawberry"
- `produce_id` (Integer, Not Null, Foreign Key to produce.id table)
- `country_code` (Varchar(2), Not Null) - ISO 3166-1 alpha-2
- `region` (Varchar, Nullable) - e.g., "California"
- `start_month` (Integer, Not Null, 1-12)
- `end_month` (Integer, Not Null, 1-12)

*Indexing:* Create a composite index on `(country_code, region, start_month, end_month)` for fast lookups.

*PostgreSQL Table: `produce`*
- `id` (Serial, Primary Key)
- `name` (Varchar, Unique, Not Null)
- `image_url` (Varchar)
- `description` (Text)

**Comprehensive API Design:**

*Custom Endpoint:* `GET /api/seasonal-produce`

**Description:** Returns a list of produce_ids that are in season for a given location.

**Query Parameters:**
- `country` (string, required)
- `region` (string, optional)
- `month` (integer, optional, defaults to current month on server)

**Success Response (200 OK):**
```json
{
  "in_season_produce_ids": [1, 5, 12, 23]
}
```

**Error Responses:**
- `400 Bad Request`: If country is missing
- `404 Not Found`: If no data is available for the location

Frontend Architecture:

The services/location.ts file will contain the logic for the entire detection flow.

A global store store/location.ts will hold the user's determined location and the list of in-season produce IDs.

The homepage (pages/index.astro) and recipe list page (pages/recipes/index.astro) will read from this store to filter the recipes they display.

The InSeasonBadge.astro component will check if a recipe's ingredients overlap with the in-season list to conditionally render a badge.

Detailed Implementation Guide (Pseudocode):

**Frontend location.ts:**

```typescript
function getSeasonalProduceIds(): Promise<number[]> {
  // 1. Check user profile for manual override
  let location = userStore.profile.location_manual_override;

  if (!location) {
    // 2. If no override, call IP geolocation API
    location = await fetch('https://ip-api.com/json').then(res => res.json());
  }

  // 3. Call our backend with the location data
  const response = await api.get('/seasonal-produce', {
    params: { country: location.countryCode, region: location.regionName }
  });

  // 4. Store result in state
  locationStore.setInSeasonIds(response.data.in_season_produce_ids);
  return response.data.in_season_produce_ids;
}
```

**Backend routes/seasonal.go:**

```go
func handleGetSeasonalProduce(c echo.Context) error {
    country := c.QueryParam("country")
    // ... get other params ...
    currentMonth := time.Now().Month()

    // 1. Create a cache key
    cacheKey := fmt.Sprintf("seasonal:%s:%s:%d", country, region, currentMonth)

    // 2. Check Redis
    cachedIds, err := redisClient.Get(cacheKey).Result()
    if err == nil {
        // Cache hit, return the data
        return c.JSON(http.StatusOK, ...)
    }

    // 3. Cache miss, query PostgreSQL
    produceIds, err := db.GetSeasonalProduceForLocation(country, region, currentMonth)
    if err != nil {
        // handle error
    }

    // 4. Store result in Redis with 24h expiration
    redisClient.Set(cacheKey, produceIds, 24 * time.Hour)

    // 5. Return response
    return c.JSON(http.StatusOK, ...)
}
```

**User Experience Flow:**

- **First Visit:** User lands on the site. A subtle loading indicator is shown over the recipe list. Geolocation runs in the background. Once complete, the recipe list updates to show seasonal items first.

- **Manual Override:** User goes to their profile, enters "Paris, France", and saves. The next time they visit the homepage, the content is tailored to the French growing season.

**Security Considerations:**

Rate limiting (as defined in the context: 100 requests/hour/IP) is applied to the `/api/seasonal-produce` endpoint to prevent abuse and scraping of the seasonal data. This is managed in Redis.

**Testing Strategy:**

- **Unit Tests:** Test the PostgreSQL query logic in the Go backend. Test the caching logic with mock Redis.
- **Integration Tests:** Test the full flow from hitting the `/api/seasonal-produce` endpoint to getting a response from a seeded test database.
- **E2E Tests:** Use a browser automation tool to mock different IP locations and verify that the correct seasonal recipes are displayed on the frontend.

**Data Management:**

- The seasonal produce data in PostgreSQL is the source of truth. It must be maintained and updated periodically. A `seed_seasonal_data.go` script will be used for initial population and future updates from verified data sources (e.g., agricultural department CSVs).
- The Redis cache is critical for performance. The cache is invalidated every 24 hours to reflect potential changes in seasonality (though unlikely to change day-to-day, it's a good refresh policy).

**Error Handling & Logging:**

- If the geolocation API fails, the system gracefully degrades: it will show a generic, non-location-specific view of popular recipes. A message like "Could not determine your location. Showing our most popular juices!" will be displayed.
- If the backend API call fails, the same fallback mechanism is used.
- The backend will log errors related to database connection failures or empty query results.

---

### Feature 3: Recipe Management System

**Feature Goal:** To establish a robust, scalable system for creating, storing, and retrieving complex juice recipes, including their ingredients, nutritional information, scientific backing, and local substitution options.

**API Relationships:**

- Uses PocketBase collections for core recipe data
- Images are uploaded to Google Cloud Storage (GCS) and served via a CDN
- A backend hook (`hooks/recipe.go`) calls an external Nutritional API (e.g., Edamam, USDA FoodData Central) to fetch and cache nutritional data when a recipe is created or updated

#### Detailed Feature Requirements

**System Architecture Overview:**

- The core data model lives in PocketBase across several linked collections
- Recipe images will be uploaded from the admin panel through PocketBase, which will be configured to use GCS as its filesystem backend. This ensures images are stored securely and delivered efficiently via Google's CDN
- When an admin saves a recipe, a `onRecordBeforeCreate/Update` hook is triggered. This Go hook parses the ingredients, queries the Nutritional API, and injects the aggregated nutritional data into a JSON field in the recipe record before it's saved to the database

**Database Schema Design:**

*PocketBase Collection: `ingredients`*
- `id` (Primary Key)
- `name` (Text, Required, Unique) - e.g., "Organic Kale"
- `produce_id` (Number, Optional) - Links to PostgreSQL produce.id for seasonality checks

*PocketBase Collection: `recipes`*
- `id` (Primary Key)
- `title` (Text, Required)
- `slug` (Text, Required, Unique)
- `author` (Relation to users, Required)
- `description` (Text, Required)
- `instructions` (Editor - i.e., Markdown/HTML, Required)
- `main_image` (File, Required) - Hosted on GCS
- `gallery_images` (File, Multiple) - Hosted on GCS
- `scientific_explanation` (Editor, Optional)
- `nutritional_info` (JSON) - Populated by backend hook, e.g., `{"calories": 150, "fat": 1, "protein": 5, "carbs": 25}`
- `is_published` (Bool, Default: false)

*PocketBase Collection: `recipe_ingredients` (Junction Table)*
- `id` (Primary Key)
- `recipe` (Relation to recipes, Required)
- `ingredient` (Relation to ingredients, Required)
- `quantity` (Text, Required) - e.g., "1 cup", "200g"

*PocketBase Collection: `ingredient_substitutions`*
- `id` (Primary Key)
- `original_ingredient` (Relation to ingredients, Required)
- `substitute_ingredient` (Relation to ingredients, Required)
- `notes` (Text, Optional) - e.g., "Use if original is out of season"

Comprehensive API Design (Leveraging PocketBase SDK):

Read Recipes:

GET /api/collections/recipes/records?filter=(is_published=true)&expand=author,recipe_ingredients(recipe).ingredient

Read Single Recipe:

GET /api/collections/recipes/records?filter=(slug='{slug}')&expand=author,recipe_ingredients(recipe).ingredient

Admin/Create/Update/Delete: Standard PocketBase record endpoints for all related collections, protected by Admin-only authorization rules.

Frontend Architecture:

pages/recipes/[slug].astro will be the main detail page. It will fetch all related data for a single recipe (details, author, ingredients) in a single API call using expand.

The IngredientSubstitutions.astro component on the recipe detail page will take the list of ingredients, query the ingredient_substitutions collection, and display available alternatives, especially highlighting any that are currently in season based on the locationStore.

Detailed CRUD Operations:

Create: An admin uses a dedicated UI. They fill in the recipe details, upload images, and select ingredients from the ingredients collection, specifying quantities. On save, a POST request is sent. The backend hook enriches the record with nutritional data before committing it.

Read: A user visits a recipe page. The frontend makes one expanded GET request to fetch the recipe and all its related ingredients. The data is displayed via RecipeDetail.astro.

Update: An admin edits a recipe. A PATCH request is sent with the changed fields. If the ingredients have changed, the nutritional data hook is re-triggered.

Delete: Admin deletes a recipe. This will perform a cascading delete. A backend hook will ensure related recipe_ingredients are also removed. Images in GCS will be deleted by another hook to prevent orphaned files.

User Experience Flow:

Admin Creates Recipe: Admin navigates to /admin/recipes/new. Fills out a form with a rich text editor for instructions. An ingredient selector allows them to search and add from the ingredients collection. On "Save & Publish", the data is sent, the backend hook runs (showing a loading state in the UI), and upon success, the admin is redirected to the new recipe page.

Security Considerations:

Write access (Create, Update, Delete) to recipes, ingredients, and related collections will be restricted to users with an "Admin" or "ContentEditor" role via PocketBase access rules.

Read access is public only for recipes where is_published = true.

The Nutritional API key will be stored securely as a server-side environment variable and never exposed to the client.

Testing Strategy:

Integration Tests: Write a test for the recipe.go hook to ensure it correctly calls the mock nutritional API and populates the nutritional_info field. Test the GCS file deletion hook.

E2E Tests: Simulate an admin creating, updating, and deleting a recipe. Simulate a user viewing a recipe and seeing all correct information, including in-season badges and substitution suggestions.

Data Management:

Recipe images are uploaded once and managed by GCS. PocketBase's thumbs feature can be used to generate optimized image sizes on the fly, which are then cached by the CDN.

Public recipe data can be cached at the CDN level for short periods (e.g., 5-10 minutes) to reduce database load for popular recipes.

Error Handling & Logging:

If the external Nutritional API call fails during recipe creation, the nutritional_info field will be left empty, and an error will be logged. The recipe will still be saved. A background job or manual process can be used to retry fetching the data later.

If an image fails to load on the frontend, a placeholder image will be shown.

Feature 4: Interactive Seasonal Produce Guide

Feature Goal: To provide users with a visually engaging, filterable guide to discover which fruits and vegetables are in season in their specific country and region, driving ingredient awareness and recipe exploration.

API Relationships:

The frontend component directly uses the GET /api/seasonal-produce and GET /api/produce/{id} endpoints defined in the backend.

Detailed Feature Requirements

System Architecture Overview:

This feature is primarily a frontend implementation. The InteractiveCalendar.tsx React component, embedded within an Astro page, will be the core of the feature.

It will maintain its own state for the selected country, region, and month. On state change, it will re-fetch data from the backend API. The backend architecture is the same as described in Feature 2 (Go endpoint -> Redis -> PostgreSQL).

Database Schema Design:

This feature relies entirely on the seasonal_produce and produce tables in the PostgreSQL database, as defined in Feature 2. No new database structures are required.

Comprehensive API Design:

Reuses GET /api/seasonal-produce (from Feature 2).

New Endpoint: GET /api/produce/:id

Description: Gets detailed information for a single produce item.

Success Response (200 OK):

{

  "id": 12,

  "name": "Hass Avocado",

  "image_url": "https://storage.googleapis.com/...",

  "description": "A creamy, nutrient-dense fruit...",

  "benefits": "Rich in healthy fats and potassium..."

}

Frontend Architecture:

The pages/seasonal-guide.astro page will host the InteractiveCalendar.tsx component.

This React component will have dropdowns for country and month. The country dropdown will be populated with a predefined list.

The main view will be a grid of ProduceDetailCard.astro components, which are rendered based on the API response. Clicking a card can open a modal with more detailed information fetched from the /api/produce/:id endpoint.

Detailed CRUD Operations:

This feature is Read-only from the user's perspective. All data management (CRUD for produce seasonality) is an administrative task handled by updating the PostgreSQL database.

User Experience Flow:

User navigates to the /seasonal-guide page. It defaults to their auto-detected location (from locationStore) and the current month. A grid of in-season produce is displayed.

User uses a dropdown to change the month to "December". The grid animates and updates to show produce available in December.

User clicks on "Avocado". A modal appears showing its image, description, and health benefits.

Security Considerations:

All endpoints are public and read-only. They are protected by the same IP-based rate limiting as the main seasonal API to prevent scraping.

Testing Strategy:

Component Tests: Test the InteractiveCalendar.tsx component to ensure that changing the month/country dropdowns triggers an API call and correctly updates the displayed produce list.

Data Management:

Data is fetched on-demand. To improve UX, data for the next and previous months could be pre-fetched in the background after the initial load.

Error Handling & Logging:

If the API returns no data for a selected location/month combination, the UI will display a user-friendly message: "No seasonal produce found for this selection. Try a different month or region!"

Feature 5: Content Management & Blog System

Feature Goal: To empower content creators with a simple yet powerful system for publishing educational articles and blog posts, enhancing SEO, building user trust, and capturing leads via downloadable resources.

API Relationships:

Core functionality uses PocketBase collections.

A custom Go service (internal/services/pdf_generator.go) is triggered via a dedicated API endpoint to generate PDFs from article content.

Generated PDFs are stored in GCS and served via a CDN.

Detailed Feature Requirements

System Architecture Overview:

PocketBase acts as a headless CMS. Admins will use the PocketBase Admin UI to create, manage, and categorize articles.

A custom route (/api/generate-pdf/:articleId) will be created in the Go backend. When hit, it fetches the article content, passes it to the pdf_generator.go service (which could use a library like gofpdf), saves the output to GCS, and returns the public URL.

The frontend dynamically renders blog pages (/blog) and individual posts (/blog/[slug]) by fetching data from the PocketBase API.

Database Schema Design:

PocketBase Collection: articles

id (Primary Key)

title (Text, Required)

slug (Text, Required, Unique)

author (Relation to users, Required)

excerpt (Text, Required)

featured_image (File, Required) - On GCS

content (Editor, Required)

category (Relation to categories)

tags (Relation to tags, Multiple)

is_published (Bool, Default: false)

seo_title (Text, Optional)

seo_description (Text, Optional)

pdf_url (URL, Optional) - Populated after PDF generation.

PocketBase Collection: categories

id (Primary Key)

name (Text, Required, Unique)

slug (Text, Required, Unique)

PocketBase Collection: tags

id (Primary Key)

name (Text, Required, Unique)

slug (Text, Required, Unique)

Comprehensive API Design:

Public Read API:

GET /api/collections/articles/records?filter=(is_published=true)&sort=-created&expand=author,category,tags - Get all articles for the blog index page.

GET /api/collections/articles/records?filter=(slug='{slug}' && is_published=true)&expand=author,category,tags - Get a single article for its detail page.

Lead Magnet API:

POST /api/generate-pdf/:articleId - (Admin only) Triggers the PDF generation process for a specific article.

POST /api/capture-lead - (Public) A simple endpoint to capture an email address before providing the pdf_url to the user. This would integrate with an email marketing service.

Admin API: Standard PocketBase endpoints for CRUD on articles, categories, and tags, protected by Admin-only rules.

Frontend Architecture:

pages/blog/index.astro: Fetches the paginated list of articles and displays them as cards.

pages/blog/[slug].astro: Fetches a single article and renders its content. It will also dynamically set the page's <title> and <meta name="description"> tags using the seo_title and seo_description fields.

A "Download PDF" component will be shown on article pages if a pdf_url exists. It will contain a form to capture the user's email before revealing the download link.

Detailed CRUD Operations:

Create: Admin uses the PocketBase UI. They write content in the rich text editor, select a category, add tags, and upload a featured image. They fill out the SEO fields. The article is saved as a draft (is_published = false).

Read (Public): A user visits /blog. They see a paginated list of published articles. They click on one and are taken to /blog/[the-slug], where the full content is rendered.

Update: Admin edits an article in the PocketBase UI and saves the changes. If the is_published flag is flipped to true, it becomes publicly visible.

Generate PDF: Admin clicks a custom button in the PocketBase UI for an article (added via UI extension or a separate admin tool) which calls POST /api/generate-pdf/:articleId. The UI polls or waits for the pdf_url field to be populated.

Delete: Admin deletes an article. A hook should ensure the corresponding PDF in GCS is also deleted.

User Experience Flow:

Lead Capture: User reads an interesting article. They see a call-to-action: "Get this guide as a PDF!" They enter their email and click "Download". The form submits, their email is sent to the lead capture service, and the button changes to a direct download link for the PDF.

Security Considerations:

All write operations are strictly limited to Admins.

The /api/generate-pdf endpoint is protected and cannot be called by the public.

The content field from the editor must be sanitized before rendering on the frontend to prevent XSS attacks. Astro's default templating provides this protection.

Testing Strategy:

Unit Tests: Test the pdf_generator.go service with sample HTML content to ensure it produces a valid, non-corrupted PDF.

Integration Tests: Test the /api/generate-pdf endpoint to ensure it correctly updates the pdf_url field on the article record.

E2E Tests: Simulate an admin creating a post, publishing it, and generating a PDF. Then, as a user, view the post and download the PDF via the lead capture form.

Data Management:

The blog index page (/blog) must use pagination (e.g., 10 articles per page) to manage performance as content grows. The PocketBase API supports this out of the box (?page=1&perPage=10).

Published articles, being mostly static, are excellent candidates for aggressive caching at the CDN level to ensure fast load times and reduce API calls. A TTL of 1-2 hours would be appropriate.

Error Handling & Logging:

If PDF generation fails, the backend will log the detailed error and the pdf_url field will not be set. The admin UI should show a "Failed" status.

If a user tries to access a blog slug that doesn't exist or isn't published, the frontend will render a custom 404 page.

Future Features (Post-MVP)
Feature 6: Premium Subscription System
Feature Goal: To introduce a recurring revenue stream by offering paid subscription tiers that unlock exclusive content and personalized features like automated meal plans and shopping lists.

API Relationships:

Frontend/Backend -> Paystack API: For initiating payments and managing subscriptions.

Paystack Webhooks -> Backend API: Paystack sends events (e.g., subscription.create, invoice.payment_failed) to a dedicated endpoint in our Go backend to update subscription statuses in real-time.

Detailed Feature Requirements
System Architecture Overview:

The system is centered around Paystack for all payment processing. Our application will not handle or store sensitive payment information.

Flow: A user selects a plan on the frontend -> The frontend requests a checkout URL from our Go backend -> The backend calls Paystack to create a session and returns the URL -> The user is redirected to Paystack to complete payment -> Paystack sends a webhook to our backend -> Our backend verifies the webhook and updates the user's subscription status in PocketBase.

A scheduled job (cron) will run weekly in the Go backend (plan_generator.go) to generate weekly plans for all active premium subscribers.

Database Schema Design:

PocketBase Collection: subscription_tiers

id (Primary Key)

name (Text, Required) - e.g., "Premium Monthly"

paystack_plan_code (Text, Required, Unique)

price (Number, Required)

currency (Text, Required) - e.g., "GHS", "USD"

features (JSON)

PocketBase Collection: user_subscriptions

id (Primary Key)

user (Relation to users, Required, Unique)

tier (Relation to subscription_tiers, Required)

paystack_customer_code (Text, Required)

paystack_subscription_code (Text, Required)

status (Select, Required) - e.g., "active", "past_due", "canceled"

expires_at (Datetime, Required)

PocketBase Collection: weekly_plans

id (Primary Key)

user (Relation to users, Required)

week_start_date (Date, Required)

recipes (Relation to recipes, Multiple)

PocketBase Collection: shopping_lists

id (Primary Key)

plan (Relation to weekly_plans, Required)

ingredients (JSON) - Aggregated list of all ingredients and quantities.

Comprehensive API Design:

GET /api/subscription-tiers: (Public) Returns all available subscription plans to display on the pricing page.

POST /api/payments/create-checkout-session: (Authenticated User) Takes a tier_id in the body. Creates a Paystack session and returns { "authorization_url": "..." }.

POST /api/payments/webhook: (Public, from Paystack) The endpoint to receive and process Paystack webhooks.

GET /api/premium/my-plan: (Premium User) Returns the current or upcoming weekly plan and shopping list for the authenticated user.

Frontend Architecture:

The user store will be updated to include subscription status (e.g., user.subscription.status).

PremiumContentLock.astro will be a wrapper component. It will check user.subscription.status and either render its children (if "active") or render an "Upgrade to Premium" CTA.

pages/premium/plans.astro will use the /api/premium/my-plan endpoint to display the weekly plan and shopping list.

Detailed CRUD Operations:

Create (Subscription): User initiates checkout. A user_subscriptions record is created with status: "pending". The Paystack webhook for a successful payment updates the status to "active".

Read (Subscription): User's subscription status is fetched on login and stored in the state.

Update (Subscription): Handled exclusively by Paystack webhooks. A charge.success event extends the expires_at date. A subscription.not_renew event sets the status to "canceled" but keeps it active until expires_at.

Delete (Subscription): A user can cancel from their profile, which calls the Paystack API to disable auto-renew. The record is not deleted, but its status is updated via webhook at the end of the billing cycle.

Generate (Plans): A weekly cron job queries for all users with status: "active". For each, it runs the plan_generator logic (considering user preferences) and creates new weekly_plans and shopping_lists records.

User Experience Flow:

A non-premium user sees a locked recipe and clicks "Unlock". They are taken to the PricingTable.astro page. They select a plan, are redirected to Paystack, enter their details, and are sent back to a .../premium/welcome page on success. Their user profile now shows an "Active Premium" badge.

Security Considerations:

Webhook Security: The webhook handler MUST validate the signature sent by Paystack in the x-paystack-signature header to ensure the request is authentic. The webhook secret key must be stored securely in the backend.

Content Gating: All API endpoints and frontend routes for premium content MUST be protected by a middleware that checks for an active subscription status in the user_subscriptions table.

Testing Strategy:

Use the Paystack CLI or a tool like ngrok to test webhooks locally.

Write integration tests for the entire subscription lifecycle: create, successful payment, failed payment, cancellation.

Write unit tests for the plan_generator.go logic to ensure it produces valid plans based on mock user preferences.

Data Management:

The state in the user_subscriptions table is the single source of truth for a user's access rights. It is managed almost entirely by automated webhooks. Manual admin overrides should be possible but logged heavily.

Error Handling & Logging:

Log every received webhook, whether its signature is valid or not.

If a webhook processing fails, the system should retry a few times. If it continues to fail, an alert should be sent to an admin for manual intervention.

Gracefully handle payment failures on the frontend by displaying a clear message from Paystack and guiding the user to update their payment method.

Feature 7: Expert Consultation Platform
Feature Goal: To increase the value of the premium subscription by offering users direct access to verified nutritionists and health experts for personalized advice and Q&A.

API Relationships:

Uses internal PocketBase collections for managing experts, bookings, and Q&A.

Could integrate with an external calendar API (Google Calendar) to sync expert availability and create events for bookings.

Could integrate with a communication API (e.g., SendGrid) for sending booking confirmations and reminders.

Detailed Feature Requirements
System Architecture Overview:

A new user role, "Expert", will be created. Experts can log in to a special dashboard to manage their profile and availability.

Availability will be managed in time slots (e.g., 30-minute blocks). An expert defines their available slots for the upcoming weeks.

Premium users can browse expert profiles and book an available slot. When a slot is booked, it is removed from the available pool.

The Q&A system is a simple, private messaging thread between a user and an expert.

Database Schema Design:

PocketBase Collection: experts

id (Primary Key)

user (Relation to users, with "Expert" role, Required)

title (Text, Required) - e.g., "Registered Dietitian"

bio (Editor, Required)

profile_image (File, Required)

credentials (File, Multiple) - For internal verification only.

is_verified (Bool, Default: false)

PocketBase Collection: expert_availability

id (Primary Key)

expert (Relation to experts, Required)

slot_start_time (Datetime, Required)

is_booked (Bool, Default: false)

PocketBase Collection: bookings

id (Primary Key)

user (Relation to users, Required)

slot (Relation to expert_availability, Required, Unique)

status (Select) - e.g., "confirmed", "completed", "canceled"

PocketBase Collection: qna_threads

id (Primary Key)

user (Relation to users, Required)

expert (Relation to experts, Required)

subject (Text, Required)

PocketBase Collection: qna_messages

id (Primary Key)

thread (Relation to qna_threads, Required)

sender (Relation to users, Required)

message (Text, Required)

Comprehensive API Design:

GET /api/experts: (Premium User) Get a list of all verified experts.

GET /api/experts/:id/availability?start_date=...&end_date=...: (Premium User) Get available slots for an expert.

POST /api/bookings: (Premium User) Book a slot. Body contains { "slot_id": "..." }.

GET /api/qna/threads: (Authenticated) Get all Q&A threads for the current user or expert.

POST /api/qna/threads/:id/messages: (Authenticated) Send a message in a thread.

Frontend Architecture:

pages/experts/index.astro to list expert profiles.

pages/experts/[id].astro to show a single expert's bio and their BookingCalendar.tsx component.

The booking calendar will fetch availability and handle the booking submission.

A new "My Q&A" section in the user's profile area will use QandAThread.astro to render the message history.

Detailed CRUD Operations:

Create (Booking): A user selects an available slot. The API call to create a bookings record should be atomic (or use a transaction) to set expert_availability.is_booked to true at the same time to prevent double bookings. An email confirmation is sent.

Read (Booking): Users can see their upcoming and past bookings in their profile.

Update (Booking): Users can cancel a booking (e.g., up to 24 hours before). This sets the status to "canceled" and makes the expert_availability slot available again.

Admin (Experts): An admin user is responsible for the full CRUD lifecycle of an experts record, including toggling is_verified after reviewing credentials.

User Experience Flow:

A premium user clicks "Ask an Expert". They browse profiles, find a nutritionist they like, and view their calendar. They select a time, click "Book", and receive an on-screen and email confirmation.

Security Considerations:

Expert credential files must be protected and accessible only to admins.

All expert-related API endpoints must be gated to premium subscribers only.

A Q&A thread and its messages must only be accessible to the participating user and expert. PocketBase rules: thread.user.id = @request.auth.id || thread.expert.user.id = @request.auth.id.

Testing Strategy:

Test the booking process for race conditions (try to have two users book the same slot simultaneously).

Test the access control rules for Q&A threads thoroughly.

Test the cancellation and slot re-availability logic.

Data Management:

Expert credential verification is a manual, offline business process. The is_verified flag is the result of this process.

Availability data is critical. A background job could be used to clean up any old, unbooked slots from the past.

Error Handling & Logging:

If a user tries to book an already-booked slot, the API should return a clear 409 Conflict error.

All booking status changes (creation, cancellation) should be logged for auditing.

Feature 8: Advanced Analytics Dashboard
Feature Goal: To integrate a comprehensive analytics platform to gain deep insights into user behavior, content performance, and revenue metrics, enabling data-driven decision-making and A/B testing.

API Relationships:

Frontend/Backend -> PostHog API: The entire application will send event data to PostHog using their native SDKs (JS for frontend, Go for backend).

Detailed Feature Requirements
System Architecture Overview:

This feature is an integration, not a system built from scratch. PostHog will be the single source of truth for analytics data.

The Astro frontend will initialize the PostHog JS snippet on page load. A wrapper service (services/analytics.ts) will be created to standardize event tracking.

The Go backend will use the PostHog Go library to track key server-side events, such as user_registered, subscription_created, and plan_generated. This provides a more reliable picture than client-side tracking alone.

The "dashboard" will primarily be the PostHog platform itself. We will not rebuild this functionality.

Database Schema Design:

No new database collections are needed in our system. All data is stored and managed within PostHog.

Comprehensive API Design:

Not applicable. All interaction is with the PostHog SDKs, not through custom APIs.

Frontend Architecture:

The services/analytics.ts wrapper will expose simple methods:

analytics.identify(userId, { email, name, subscription_status }) - Called on login.

analytics.track(eventName, { property1, property2 }) - Called on specific actions.

analytics.reset() - Called on logout.

This service will be used across all relevant components. For example, RecipeCard.astro will call analytics.track('recipe_viewed', { recipe_id, recipe_title }). The PricingTable.astro will track upgrade_button_clicked.

Detailed CRUD Operations:

Not applicable. The primary operations are identify a user and capture an event.

User Experience Flow:

For the end-user: The experience is seamless and invisible. Analytics tracking runs in the background.

For the admin/analyst: They log in to the company's PostHog account. They can build funnels (e.g., "Viewed Pricing Page" -> "Clicked Checkout" -> "Subscription Active"), analyze user paths, view revenue metrics from subscription_created events, and set up A/B tests.

Security Considerations:

The public PostHog API key (for the client-side) and the private key (for the server-side) must be stored securely as environment variables.

Ensure that no sensitive Personal Identifiable Information (PII) beyond what is necessary (like email for user identification) is sent to PostHog. For example, do not send password reset tokens or health data from user preferences.

Testing Strategy:

Use the PostHog debugger or live event view during development to confirm that events are being captured correctly from both the frontend and backend.

Write E2E tests that perform key actions and then use the PostHog API (or manual check) to verify that the corresponding events were created for the test user.

Data Management:

A tracking plan or event taxonomy must be created and maintained. This document will define every event name and its associated properties. This is crucial for preventing a messy, inconsistent analytics implementation.

Example Events: account_created, login, recipe_viewed, seasonal_guide_filtered, subscription_checkout_started, subscription_created, booking_created.

Error Handling & Logging:

- The `analytics.ts` wrapper on the frontend should contain try-catch blocks so that if the PostHog script fails to load or an event fails to send, it does not crash the user-facing application. The failure should be logged to the browser console.
- The backend should similarly handle errors from the PostHog SDK gracefully.

---

## Conclusion

This comprehensive feature expansion response outlines the complete architecture and implementation strategy for PhreshStart's core features and future enhancements. The modular approach ensures scalability while maintaining clear separation of concerns between frontend and backend systems.

Each feature has been designed with security, performance, and user experience as primary considerations, leveraging modern technologies like PocketBase, PostgreSQL, Redis, and cloud services to deliver a robust and scalable platform.