import { MigrationResult, TeamMember, PocketBaseClient } from "./deps.ts";
import { logProgress, handleError, parseFrontmatter, generateMemberId, uploadAsset } from "./utils.ts";

/**
 * Migrate team members from about.md files to PocketBase
 */
export async function migrateTeamMembers(pb: PocketBaseClient): Promise<MigrationResult> {
  logProgress("TEAM_MEMBERS", "Starting team members migration...");
  
  const languages = ['english', 'french'];
  let totalMigrated = 0;
  const errors: string[] = [];
  
  try {
    // Clear existing team members to avoid duplicates
    await clearExistingTeamMembers(pb);
    
    for (const lang of languages) {
      const aboutFile = `content/${lang}/about.md`;
      
      try {
        const content = await Deno.readTextFile(aboutFile);
        const { frontmatter } = parseFrontmatter(content);
        
        logProgress("TEAM_MEMBERS", `Processing ${aboutFile}...`);
        
        // Extract team members from leadership section
        const frontmatterAny = frontmatter as Record<string, unknown>;
        logProgress("TEAM_MEMBERS", `Checking frontmatter structure for ${aboutFile}`);

        if (frontmatterAny.leadership && typeof frontmatterAny.leadership === 'object') {
          const leadership = frontmatterAny.leadership as Record<string, unknown>;
          logProgress("TEAM_MEMBERS", `Found leadership section in ${aboutFile}`);

          if (leadership.management && typeof leadership.management === 'object') {
            const management = leadership.management as Record<string, unknown>;
            logProgress("TEAM_MEMBERS", `Found management section in ${aboutFile}`);

            if (management.members && Array.isArray(management.members)) {
              logProgress("TEAM_MEMBERS", `Found ${management.members.length} team members in ${aboutFile}`);
              const count = await migrateManagementTeam(pb, management.members as TeamMember[], lang);
              totalMigrated += count;
              logProgress("TEAM_MEMBERS", `Migrated ${count} management team members for ${lang}`);
            } else {
              logProgress("TEAM_MEMBERS", `No members array found in management section of ${aboutFile}`);
            }
          } else {
            logProgress("TEAM_MEMBERS", `No management section found in leadership of ${aboutFile}`);
          }
        } else {
          logProgress("TEAM_MEMBERS", `No leadership section found in ${aboutFile}`);
        }

        // Also check for team_member array (from team.md files)
        if (frontmatterAny.team_member && Array.isArray(frontmatterAny.team_member)) {
          const count = await migrateTeamMemberArray(pb, frontmatterAny.team_member as unknown[], lang);
          totalMigrated += count;
          logProgress("TEAM_MEMBERS", `Migrated ${count} team members from team_member array for ${lang}`);
        }
        
      } catch (error) {
        const errorMsg = `Failed to process ${aboutFile}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.warn(`⚠️ ${errorMsg}`);
      }
    }
    
    // Also check team.md files
    for (const lang of languages) {
      const teamFile = `content/${lang}/team.md`;
      
      try {
        const content = await Deno.readTextFile(teamFile);
        const { frontmatter } = parseFrontmatter(content);
        
        const frontmatterAny = frontmatter as Record<string, unknown>;
        if (frontmatterAny.team_member && Array.isArray(frontmatterAny.team_member)) {
          const count = await migrateTeamMemberArray(pb, frontmatterAny.team_member as unknown[], lang);
          totalMigrated += count;
          logProgress("TEAM_MEMBERS", `Migrated ${count} team members from ${teamFile}`);
        }
      } catch (error) {
        // team.md might not exist, that's okay
        console.warn(`⚠️ Could not process ${teamFile}:`, error instanceof Error ? error.message : String(error));
      }
    }
    
    if (errors.length > 0 && totalMigrated === 0) {
      return {
        success: false,
        message: "Team members migration failed completely",
        error: errors.join('; ')
      };
    }
    
    return {
      success: true,
      message: `Successfully migrated ${totalMigrated} team members`,
      recordId: `${totalMigrated}_members`
    };
    
  } catch (error) {
    handleError("TEAM_MEMBERS", error);
    return {
      success: false,
      message: "Team members migration failed",
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Clear existing team members to avoid duplicates
 */
async function clearExistingTeamMembers(pb: PocketBaseClient): Promise<void> {
  try {
    const existingMembers = await pb.collection('team_members').getFullList();
    
    if (existingMembers.length > 0) {
      logProgress("TEAM_MEMBERS", `Clearing ${existingMembers.length} existing team members...`);
      
      for (const member of existingMembers) {
        await pb.collection('team_members').delete(member.id);
      }
      
      logProgress("TEAM_MEMBERS", "Existing team members cleared");
    }
  } catch (error) {
    console.warn("⚠️ Failed to clear existing team members:", error);
    // Continue with migration even if clearing fails
  }
}

/**
 * Migrate management team members from leadership section
 */
async function migrateManagementTeam(pb: PocketBaseClient, members: TeamMember[], language: string): Promise<number> {
  let count = 0;
  
  for (const member of members) {
    try {
      const teamMember = {
        member_id: generateMemberId(member.name),
        language: language === 'english' ? 'en' : 'fr',
        name: member.name,
        position: member.position,
        bio: member.bio || '',
        department: 'Management',
        active: true,
        sort_order: count,
        translation_status: 'published'
      };
      
      const result = await pb.collection('team_members').create(teamMember);
      
      // Upload photo separately
      if (member.image) {
        await uploadMemberPhoto(pb, member.image, result.id);
      }
      
      logProgress("TEAM_MEMBERS", `Created team member: ${teamMember.name} (${language})`);
      count++;
      
    } catch (error) {
      console.warn(`⚠️ Failed to create team member "${member.name}":`, error);
    }
  }
  
  return count;
}

/**
 * Migrate team members from team_member array
 */
async function migrateTeamMemberArray(pb: PocketBaseClient, members: unknown[], language: string): Promise<number> {
  let count = 0;
  
  for (const member of members) {
    try {
      const memberObj = member as Record<string, unknown>;
      const teamMember = {
        member_id: generateMemberId(memberObj.name as string),
        language: language === 'english' ? 'en' : 'fr',
        name: memberObj.name as string,
        position: (memberObj.designation as string) || (memberObj.position as string) || '',
        bio: (memberObj.bio as string) || '',
        department: (memberObj.department as string) || 'General',
        active: true,
        sort_order: count,
        translation_status: 'published'
      };

      const result = await pb.collection('team_members').create(teamMember);

      // Upload photo separately
      if (memberObj.image) {
        await uploadMemberPhoto(pb, memberObj.image as string, result.id);
      }
      
      logProgress("TEAM_MEMBERS", `Created team member: ${teamMember.name} (${language})`);
      count++;
      
    } catch (error) {
      const memberObj = member as Record<string, unknown>;
      console.warn(`⚠️ Failed to create team member "${memberObj.name}":`, error);
    }
  }
  
  return count;
}

/**
 * Upload member photo
 */
async function uploadMemberPhoto(pb: PocketBaseClient, imagePath: string, memberId: string): Promise<void> {
  try {
    logProgress("TEAM_MEMBERS", `Uploading photo: ${imagePath}`);
    const photoFormData = await uploadAsset(pb, imagePath);
    
    await pb.collection('team_members').update(memberId, {
      photo: photoFormData.get('file')
    });
    
    logProgress("TEAM_MEMBERS", "Photo uploaded successfully");
  } catch (error) {
    console.warn(`⚠️ Failed to upload photo ${imagePath}:`, error);
    // Don't fail the member migration for photo upload issues
  }
}

// Run migration if script is executed directly
if (import.meta.main) {
  const { initializePocketBase } = await import("./utils.ts");
  
  try {
    const pb = await initializePocketBase();
    const result = await migrateTeamMembers(pb);
    
    if (result.success) {
      console.log("✅ Team members migration completed successfully");
    } else {
      console.error("❌ Team members migration failed:", result.error);
      Deno.exit(1);
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    Deno.exit(1);
  }
}
