import { parseToml, MigrationResult, MenuItem, PocketBaseClient } from "./deps.ts";
import { logProgress, handleError } from "./utils.ts";

/**
 * Migrate navigation menus from TOML files to PocketBase
 */
export async function migrateMenus(pb: PocketBaseClient): Promise<MigrationResult> {
  logProgress("MENUS", "Starting navigation menus migration...");
  
  const menuFiles = [
    { file: 'config/_default/menus.en.toml', language: 'en' },
    { file: 'config/_default/menus.fr.toml', language: 'fr' }
  ];
  
  let totalMigrated = 0;
  const errors: string[] = [];
  
  try {
    // Clear existing menu items to avoid duplicates
    await clearExistingMenus(pb);
    
    for (const { file, language } of menuFiles) {
      try {
        const menuContent = await Deno.readTextFile(file);
        const menuData = parseToml(menuContent) as any;
        
        logProgress("MENUS", `Processing ${file}...`);
        
        // Migrate main menu
        if (menuData.main) {
          const mainCount = await migrateMenuItems(pb, menuData.main, 'main', language);
          totalMigrated += mainCount;
          logProgress("MENUS", `Migrated ${mainCount} main menu items for ${language}`);
        }
        
        // Migrate footer menu
        if (menuData.footer) {
          const footerCount = await migrateMenuItems(pb, menuData.footer, 'footer', language);
          totalMigrated += footerCount;
          logProgress("MENUS", `Migrated ${footerCount} footer menu items for ${language}`);
        }
        
      } catch (error) {
        const errorMsg = `Failed to process ${file}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.warn(`⚠️ ${errorMsg}`);
      }
    }
    
    if (errors.length > 0 && totalMigrated === 0) {
      return {
        success: false,
        message: "Menu migration failed completely",
        error: errors.join('; ')
      };
    }
    
    return {
      success: true,
      message: `Successfully migrated ${totalMigrated} menu items`,
      recordId: `${totalMigrated}_items`
    };
    
  } catch (error) {
    handleError("MENUS", error);
    return {
      success: false,
      message: "Menu migration failed",
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Clear existing menu items to avoid duplicates
 */
async function clearExistingMenus(pb: PocketBaseClient): Promise<void> {
  try {
    const existingMenus = await pb.collection('navigation_menus').getFullList();
    
    if (existingMenus.length > 0) {
      logProgress("MENUS", `Clearing ${existingMenus.length} existing menu items...`);
      
      for (const menu of existingMenus) {
        await pb.collection('navigation_menus').delete(menu.id);
      }
      
      logProgress("MENUS", "Existing menu items cleared");
    }
  } catch (error) {
    console.warn("⚠️ Failed to clear existing menus:", error);
    // Continue with migration even if clearing fails
  }
}

/**
 * Migrate menu items for a specific menu type and language
 */
async function migrateMenuItems(pb: PocketBaseClient, items: MenuItem[], menuName: string, language: string): Promise<number> {
  let count = 0;
  
  for (const item of items) {
    try {
      const menuItem = {
        menu_name: menuName,
        label: item.name,
        url: item.URL.startsWith('/') ? item.URL : `/${item.URL}`,
        weight: item.weight || 0,
        language: language,
        active: true,
        target: '_self',
        menu_depth: 0
      };
      
      await pb.collection('navigation_menus').create(menuItem);
      logProgress("MENUS", `Created menu item: ${menuItem.label} (${language})`);
      count++;
      
    } catch (error) {
      console.warn(`⚠️ Failed to create menu item "${item.name}":`, error);
    }
  }
  
  return count;
}

/**
 * Validate menu structure
 */
export async function validateMenuStructure(): Promise<boolean> {
  const menuFiles = [
    'config/_default/menus.en.toml',
    'config/_default/menus.fr.toml'
  ];
  
  let hasValidMenus = false;
  
  for (const file of menuFiles) {
    try {
      const content = await Deno.readTextFile(file);
      const menuData = parseToml(content) as any;
      
      if (menuData.main || menuData.footer) {
        hasValidMenus = true;
        logProgress("VALIDATION", `Valid menu structure found in ${file}`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not validate ${file}:`, error instanceof Error ? error.message : String(error));
    }
  }
  
  return hasValidMenus;
}

// Run migration if script is executed directly
if (import.meta.main) {
  const { initializePocketBase } = await import("./utils.ts");
  
  try {
    // Validate menu structure first
    const isValid = await validateMenuStructure();
    if (!isValid) {
      console.error("❌ No valid menu files found");
      Deno.exit(1);
    }
    
    const pb = await initializePocketBase();
    const result = await migrateMenus(pb);
    
    if (result.success) {
      console.log("✅ Menu migration completed successfully");
    } else {
      console.error("❌ Menu migration failed:", result.error);
      Deno.exit(1);
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    Deno.exit(1);
  }
}
