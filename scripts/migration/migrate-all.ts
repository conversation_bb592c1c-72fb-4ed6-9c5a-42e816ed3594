import { MigrationStats, logProgress, handleError, initializePocketBase, createBackupDir, copyDirectory } from "./utils.ts";
import { analyzeExistingContent, validateContentStructure } from "./analyze-content.ts";
import { migrateSiteSettings } from "./migrate-site-settings.ts";
import { migrateMenus } from "./migrate-menus.ts";
import { migratePages } from "./migrate-pages.ts";
import { migrateTeamMembers } from "./migrate-team-members.ts";

/**
 * Main migration orchestrator
 */
export async function migrateAllContent(): Promise<void> {
  const startTime = Date.now();
  logProgress("MIGRATION", "🚀 Starting complete content migration...");
  
  const stats: MigrationStats = {
    siteSettings: 0,
    navigationMenus: 0,
    pages: 0,
    teamMembers: 0,
    assets: 0,
    errors: []
  };
  
  try {
    // 1. Validate environment and content structure
    logProgress("MIGRATION", "📋 Validating environment and content structure...");
    const isValid = await validateContentStructure();
    if (!isValid) {
      throw new Error("Content structure validation failed");
    }
    
    // 2. Analyze existing content
    logProgress("MIGRATION", "📊 Analyzing existing content...");
    const inventory = await analyzeExistingContent();
    logProgress("MIGRATION", `Found ${inventory.pages.english.length + inventory.pages.french.length} pages, ${inventory.configs.length} configs, ${inventory.assets.length} assets`);
    
    // 3. Create backup
    logProgress("MIGRATION", "💾 Creating backup...");
    const backupDir = await createBackupDir();
    await createBackup(backupDir);
    
    // 4. Initialize PocketBase
    logProgress("MIGRATION", "🔐 Initializing PocketBase connection...");
    const pb = await initializePocketBase();
    
    // 5. Run migrations in sequence
    await runMigrations(pb, stats);
    
    // 6. Validate migration results
    logProgress("MIGRATION", "✅ Validating migration results...");
    await validateMigration(pb, stats);
    
    // 7. Generate final report
    const duration = (Date.now() - startTime) / 1000;
    await generateMigrationReport(stats, duration, backupDir);
    
    logProgress("MIGRATION", "🎉 Migration completed successfully!");
    console.log("\n📊 Migration Summary:");
    console.log(`   Site Settings: ${stats.siteSettings}`);
    console.log(`   Navigation Menus: ${stats.navigationMenus}`);
    console.log(`   Pages: ${stats.pages}`);
    console.log(`   Team Members: ${stats.teamMembers}`);
    console.log(`   Duration: ${duration.toFixed(2)}s`);
    console.log(`   Backup: ${backupDir}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  Warnings (${stats.errors.length}):`);
      stats.errors.forEach(error => console.log(`   - ${error}`));
    }
    
  } catch (error) {
    handleError("MIGRATION", error);
    
    // Attempt rollback
    logProgress("MIGRATION", "🔄 Migration failed, attempting rollback...");
    await attemptRollback();
    
    throw error;
  }
}

/**
 * Run all migration steps
 */
async function runMigrations(pb: any, stats: MigrationStats): Promise<void> {
  // 1. Migrate site settings
  logProgress("MIGRATION", "⚙️ Migrating site settings...");
  try {
    const siteResult = await migrateSiteSettings(pb);
    if (siteResult.success) {
      stats.siteSettings = 1;
    } else {
      stats.errors.push(`Site settings: ${siteResult.error}`);
    }
  } catch (error) {
    stats.errors.push(`Site settings: ${error.message}`);
  }
  
  // 2. Migrate navigation menus
  logProgress("MIGRATION", "🧭 Migrating navigation menus...");
  try {
    const menuResult = await migrateMenus(pb);
    if (menuResult.success) {
      const count = parseInt(menuResult.recordId?.split('_')[0] || '0');
      stats.navigationMenus = count;
    } else {
      stats.errors.push(`Navigation menus: ${menuResult.error}`);
    }
  } catch (error) {
    stats.errors.push(`Navigation menus: ${error.message}`);
  }
  
  // 3. Migrate pages
  logProgress("MIGRATION", "📄 Migrating pages...");
  try {
    const pagesResult = await migratePages(pb);
    if (pagesResult.success) {
      const count = parseInt(pagesResult.recordId?.split('_')[0] || '0');
      stats.pages = count;
    } else {
      stats.errors.push(`Pages: ${pagesResult.error}`);
    }
  } catch (error) {
    stats.errors.push(`Pages: ${error.message}`);
  }
  
  // 4. Migrate team members
  logProgress("MIGRATION", "👥 Migrating team members...");
  try {
    const teamResult = await migrateTeamMembers(pb);
    if (teamResult.success) {
      const count = parseInt(teamResult.recordId?.split('_')[0] || '0');
      stats.teamMembers = count;
    } else {
      stats.errors.push(`Team members: ${teamResult.error}`);
    }
  } catch (error) {
    stats.errors.push(`Team members: ${error.message}`);
  }
}

/**
 * Create backup of existing content
 */
async function createBackup(backupDir: string): Promise<void> {
  try {
    // Copy content files
    await copyDirectory('content', `${backupDir}/content`);
    await copyDirectory('config', `${backupDir}/config`);
    await copyDirectory('static', `${backupDir}/static`);
    
    logProgress("MIGRATION", `Backup created: ${backupDir}`);
  } catch (error) {
    console.warn("⚠️ Backup creation failed:", error);
    // Don't fail migration for backup issues
  }
}

/**
 * Validate migration results
 */
async function validateMigration(pb: any, stats: MigrationStats): Promise<void> {
  const validations = [
    { collection: 'site_settings', expected: 1, actual: stats.siteSettings },
    { collection: 'navigation_menus', expected: 'multiple', actual: stats.navigationMenus },
    { collection: 'pages', expected: 'multiple', actual: stats.pages },
    { collection: 'team_members', expected: 'multiple', actual: stats.teamMembers }
  ];
  
  for (const validation of validations) {
    try {
      const records = await pb.collection(validation.collection).getFullList();
      
      if (validation.expected === 1 && records.length !== 1) {
        stats.errors.push(`Expected 1 ${validation.collection} record, got ${records.length}`);
      } else if (validation.expected === 'multiple' && records.length === 0) {
        stats.errors.push(`Expected multiple ${validation.collection} records, got 0`);
      }
      
      logProgress("VALIDATION", `✅ ${validation.collection}: ${records.length} records`);
    } catch (error) {
      stats.errors.push(`Validation failed for ${validation.collection}: ${error.message}`);
    }
  }
}

/**
 * Generate migration report
 */
async function generateMigrationReport(stats: MigrationStats, duration: number, backupDir: string): Promise<void> {
  const report = {
    timestamp: new Date().toISOString(),
    duration: `${duration.toFixed(2)}s`,
    backup: backupDir,
    results: stats,
    success: stats.errors.length === 0
  };
  
  const reportPath = `migration-report-${new Date().toISOString().split('T')[0]}.json`;
  await Deno.writeTextFile(reportPath, JSON.stringify(report, null, 2));
  
  logProgress("MIGRATION", `Migration report saved: ${reportPath}`);
}

/**
 * Attempt rollback on failure
 */
async function attemptRollback(): Promise<void> {
  try {
    logProgress("ROLLBACK", "Rollback procedures would be implemented here");
    // TODO: Implement actual rollback logic
    // This would involve restoring from backup and clearing PocketBase collections
  } catch (error) {
    console.error("❌ Rollback failed:", error);
  }
}

// Run migration if script is executed directly
if (import.meta.main) {
  try {
    await migrateAllContent();
    console.log("\n✅ Migration completed successfully!");
  } catch (error) {
    console.error("\n❌ Migration failed:", error);
    Deno.exit(1);
  }
}
