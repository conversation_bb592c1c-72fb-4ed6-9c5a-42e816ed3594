import { parseToml, MigrationResult, PocketBaseClient } from "./deps.ts";
import { logProgress, handleError, uploadAsset } from "./utils.ts";

/**
 * Migrate site settings from params.toml to PocketBase
 */
export async function migrateSiteSettings(pb: PocketBaseClient): Promise<MigrationResult> {
  logProgress("SITE_SETTINGS", "Starting site settings migration...");
  
  try {
    // Read params.toml
    const paramsPath = "config/_default/params.toml";
    let paramsContent: string;
    
    try {
      paramsContent = await Deno.readTextFile(paramsPath);
    } catch (error) {
      return {
        success: false,
        message: `Failed to read ${paramsPath}`,
        error: error instanceof Error ? error.message : String(error)
      };
    }
    
    const params = parseToml(paramsContent) as Record<string, unknown>;
    logProgress("SITE_SETTINGS", "Parsed params.toml successfully");
    
    // Extract social links if they exist
    const socialLinks = (params.social as unknown[]) || [];
    const socialData: Record<string, string> = {};

    socialLinks.forEach((social: unknown) => {
      const socialObj = social as Record<string, unknown>;
      if (socialObj.title === 'linkedin') {
        socialData.social_linkedin = socialObj.link as string;
      } else if (socialObj.title === 'facebook') {
        socialData.social_facebook = socialObj.link as string;
      } else if (socialObj.title === 'twitter') {
        socialData.social_twitter = socialObj.link as string;
      } else if (socialObj.title === 'instagram') {
        socialData.social_instagram = socialObj.link as string;
      }
    });
    
    // Create site settings record
    const siteSettings = {
      company_name: "Power Telco Business Limited",
      company_tagline: "Empowering connectivity through fiber network infrastructure",
      company_description: params.description || "Power Telco Business Limited",
      contact_email: params.email || "<EMAIL>",
      contact_phone: params.mobile || "+233 24-388-9991",
      contact_address: params.address || "2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium",
      meta_title: params.description || "Power Telco Business Limited",
      meta_description: params.description || "Power Telco Business Limited",
      copyright_text: "© 2024 Power Telco Business Limited. All rights reserved.",
      maintenance_mode: false,
      google_analytics_id: params.google_analitycs_id || "",
      ...socialData
    };
    
    logProgress("SITE_SETTINGS", "Prepared site settings data");
    
    // Check if site settings already exist
    try {
      const existingSettings = await pb.collection('site_settings').getFullList();
      if (existingSettings.length > 0) {
        logProgress("SITE_SETTINGS", "Site settings already exist, updating...");
        
        // Update existing record
        const result = await pb.collection('site_settings').update(existingSettings[0].id, siteSettings);
        
        // Handle file uploads separately for updates
        if (params.logo) {
          await uploadLogoAndFavicon(pb, params, result.id);
        }
        
        return {
          success: true,
          message: "Site settings updated successfully",
          recordId: result.id
        };
      }
    } catch (_error) {
      // No existing settings, continue with creation
      logProgress("SITE_SETTINGS", "No existing settings found, creating new...");
    }
    
    // Create new record
    const result = await pb.collection('site_settings').create(siteSettings);
    logProgress("SITE_SETTINGS", `Site settings created with ID: ${result.id}`);
    
    // Handle file uploads for new record
    if (params.logo || params.favicon) {
      await uploadLogoAndFavicon(pb, params, result.id);
    }
    
    return {
      success: true,
      message: "Site settings migrated successfully",
      recordId: result.id
    };
    
  } catch (error) {
    handleError("SITE_SETTINGS", error);
    return {
      success: false,
      message: "Site settings migration failed",
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Upload logo and favicon files
 */
async function uploadLogoAndFavicon(pb: PocketBaseClient, params: Record<string, unknown>, recordId: string): Promise<void> {
  const updates: Record<string, unknown> = {};
  
  try {
    // Upload logo
    if (params.logo) {
      logProgress("SITE_SETTINGS", `Uploading logo: ${params.logo}`);
      const logoFormData = await uploadAsset(pb, params.logo as string);
      updates.logo = logoFormData.get('file');
    }

    // Upload favicon
    if (params.favicon) {
      logProgress("SITE_SETTINGS", `Uploading favicon: ${params.favicon}`);
      const faviconFormData = await uploadAsset(pb, params.favicon as string);
      updates.favicon = faviconFormData.get('file');
    }
    
    // Update record with files
    if (Object.keys(updates).length > 0) {
      await pb.collection('site_settings').update(recordId, updates);
      logProgress("SITE_SETTINGS", "Logo and favicon uploaded successfully");
    }
    
  } catch (error) {
    console.warn("⚠️ Failed to upload logo/favicon:", error);
    // Don't fail the entire migration for file upload issues
  }
}

// Run migration if script is executed directly
if (import.meta.main) {
  const { initializePocketBase } = await import("./utils.ts");
  
  try {
    const pb = await initializePocketBase();
    const result = await migrateSiteSettings(pb);
    
    if (result.success) {
      console.log("✅ Site settings migration completed successfully");
    } else {
      console.error("❌ Site settings migration failed:", result.error);
      Deno.exit(1);
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    Deno.exit(1);
  }
}
