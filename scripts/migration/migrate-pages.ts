import { walk, MigrationResult, PocketBaseClient } from "./deps.ts";
import { logProgress, handleError, parseFrontmatter, generateSlug, generatePageId, uploadAsset } from "./utils.ts";

/**
 * Migrate pages from Hugo markdown files to PocketBase
 */
export async function migratePages(pb: PocketBaseClient): Promise<MigrationResult> {
  logProgress("PAGES", "Starting pages migration...");
  
  const languages = ['english', 'french'];
  let totalMigrated = 0;
  const errors: string[] = [];
  
  try {
    // Clear existing pages to avoid duplicates
    await clearExistingPages(pb);
    
    for (const lang of languages) {
      const contentDir = `content/${lang}`;
      
      try {
        await Deno.stat(contentDir);
      } catch {
        console.warn(`⚠️ Content directory not found: ${contentDir}`);
        continue;
      }
      
      logProgress("PAGES", `Processing ${lang} pages...`);
      
      for await (const entry of walk(contentDir)) {
        if (entry.isFile && entry.path.endsWith('.md')) {
          try {
            const result = await migratePage(pb, entry.path, lang);
            if (result.success) {
              totalMigrated++;
            } else {
              errors.push(`${entry.path}: ${result.error}`);
            }
          } catch (error) {
            const errorMsg = `Failed to migrate ${entry.path}: ${error instanceof Error ? error.message : String(error)}`;
            errors.push(errorMsg);
            console.warn(`⚠️ ${errorMsg}`);
          }
        }
      }
    }
    
    if (errors.length > 0 && totalMigrated === 0) {
      return {
        success: false,
        message: "Pages migration failed completely",
        error: errors.join('; ')
      };
    }
    
    return {
      success: true,
      message: `Successfully migrated ${totalMigrated} pages`,
      recordId: `${totalMigrated}_pages`
    };
    
  } catch (error) {
    handleError("PAGES", error);
    return {
      success: false,
      message: "Pages migration failed",
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Clear existing pages to avoid duplicates
 */
async function clearExistingPages(pb: PocketBaseClient): Promise<void> {
  try {
    const existingPages = await pb.collection('pages').getFullList();
    
    if (existingPages.length > 0) {
      logProgress("PAGES", `Clearing ${existingPages.length} existing pages...`);
      
      for (const page of existingPages) {
        await pb.collection('pages').delete(page.id);
      }
      
      logProgress("PAGES", "Existing pages cleared");
    }
  } catch (error) {
    console.warn("⚠️ Failed to clear existing pages:", error);
    // Continue with migration even if clearing fails
  }
}

/**
 * Migrate a single page
 */
async function migratePage(pb: PocketBaseClient, filePath: string, language: string): Promise<MigrationResult> {
  try {
    const content = await Deno.readTextFile(filePath);
    const { frontmatter, body } = parseFrontmatter(content);
    
    // Generate slug and page ID
    const slug = generateSlug(filePath, language);
    const pageId = generatePageId(slug);
    
    const pageData = {
      page_id: pageId,
      language: language === 'english' ? 'en' : 'fr',
      title: frontmatter.title || 'Untitled',
      slug: slug,
      content: body,
      meta_title: frontmatter.title || '',
      meta_description: frontmatter.description || '',
      watermark: frontmatter.watermark || '',
      layout: frontmatter.layout || 'default',
      status: frontmatter.draft === true ? 'draft' : 'published',
      translation_status: 'published'
    };
    
    // Create page record
    const result = await pb.collection('pages').create(pageData);
    
    // Handle featured image upload separately
    if (frontmatter.page_header_image) {
      await uploadFeaturedImage(pb, frontmatter.page_header_image, result.id);
    }
    
    logProgress("PAGES", `Migrated page: ${pageData.title} (${language})`);
    
    return {
      success: true,
      message: `Page migrated: ${pageData.title}`,
      recordId: result.id
    };
    
  } catch (error) {
    return {
      success: false,
      message: `Failed to migrate page: ${filePath}`,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Upload featured image for a page
 */
async function uploadFeaturedImage(pb: PocketBaseClient, imagePath: string, pageId: string): Promise<void> {
  try {
    logProgress("PAGES", `Uploading featured image: ${imagePath}`);
    const imageFormData = await uploadAsset(pb, imagePath);
    
    await pb.collection('pages').update(pageId, {
      featured_image: imageFormData.get('file')
    });
    
    logProgress("PAGES", "Featured image uploaded successfully");
  } catch (error) {
    console.warn(`⚠️ Failed to upload featured image ${imagePath}:`, error);
    // Don't fail the page migration for image upload issues
  }
}

/**
 * Validate pages structure
 */
export async function validatePagesStructure(): Promise<boolean> {
  const contentDirs = ['content/english', 'content/french'];
  let hasValidPages = false;
  
  for (const dir of contentDirs) {
    try {
      const stat = await Deno.stat(dir);
      if (stat.isDirectory) {
        // Check if directory has any .md files
        for await (const entry of walk(dir)) {
          if (entry.isFile && entry.path.endsWith('.md')) {
            hasValidPages = true;
            break;
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ Could not validate ${dir}:`, error instanceof Error ? error.message : String(error));
    }
  }
  
  return hasValidPages;
}

// Run migration if script is executed directly
if (import.meta.main) {
  const { initializePocketBase } = await import("./utils.ts");
  
  try {
    // Validate pages structure first
    const isValid = await validatePagesStructure();
    if (!isValid) {
      console.error("❌ No valid page files found");
      Deno.exit(1);
    }
    
    const pb = await initializePocketBase();
    const result = await migratePages(pb);
    
    if (result.success) {
      console.log("✅ Pages migration completed successfully");
    } else {
      console.error("❌ Pages migration failed:", result.error);
      Deno.exit(1);
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    Deno.exit(1);
  }
}
