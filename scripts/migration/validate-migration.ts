import { logProgress, initializePocketBase } from "./utils.ts";

/**
 * Comprehensive migration validation
 */
export async function validateMigration(): Promise<boolean> {
  logProgress("VALIDATION", "🔍 Starting migration validation...");
  
  try {
    const pb = await initializePocketBase();
    
    const validations = [
      () => validateSiteSettings(pb),
      () => validateNavigationMenus(pb),
      () => validatePages(pb),
      () => validateTeamMembers(pb),
      () => validateDataIntegrity(pb)
    ];
    
    let allValid = true;
    
    for (const validation of validations) {
      const isValid = await validation();
      if (!isValid) {
        allValid = false;
      }
    }
    
    if (allValid) {
      logProgress("VALIDATION", "✅ All validations passed!");
    } else {
      logProgress("VALIDATION", "❌ Some validations failed");
    }
    
    return allValid;
    
  } catch (error) {
    console.error("❌ Validation failed:", error);
    return false;
  }
}

/**
 * Validate site settings migration
 */
async function validateSiteSettings(pb: any): Promise<boolean> {
  try {
    const records = await pb.collection('site_settings').getFullList();
    
    if (records.length !== 1) {
      console.error(`❌ Expected 1 site_settings record, got ${records.length}`);
      return false;
    }
    
    const settings = records[0];
    const requiredFields = ['company_name', 'contact_email'];
    
    for (const field of requiredFields) {
      if (!settings[field]) {
        console.error(`❌ Site settings missing required field: ${field}`);
        return false;
      }
    }
    
    logProgress("VALIDATION", "✅ Site settings validation passed");
    return true;
    
  } catch (error) {
    console.error("❌ Site settings validation failed:", error);
    return false;
  }
}

/**
 * Validate navigation menus migration
 */
async function validateNavigationMenus(pb: any): Promise<boolean> {
  try {
    const records = await pb.collection('navigation_menus').getFullList();
    
    if (records.length === 0) {
      console.error("❌ No navigation menu records found");
      return false;
    }
    
    // Check for both languages
    const languages = ['en', 'fr'];
    const menuTypes = ['main', 'footer'];
    
    for (const lang of languages) {
      for (const menuType of menuTypes) {
        const menuItems = records.filter(r => r.language === lang && r.menu_name === menuType);
        if (menuItems.length === 0) {
          console.warn(`⚠️ No ${menuType} menu items found for language: ${lang}`);
        }
      }
    }
    
    // Validate required fields
    for (const menu of records) {
      if (!menu.label || !menu.language || !menu.menu_name) {
        console.error("❌ Navigation menu missing required fields");
        return false;
      }
    }
    
    logProgress("VALIDATION", `✅ Navigation menus validation passed (${records.length} items)`);
    return true;
    
  } catch (error) {
    console.error("❌ Navigation menus validation failed:", error);
    return false;
  }
}

/**
 * Validate pages migration
 */
async function validatePages(pb: any): Promise<boolean> {
  try {
    const records = await pb.collection('pages').getFullList();
    
    if (records.length === 0) {
      console.error("❌ No page records found");
      return false;
    }
    
    // Check for both languages
    const languages = ['en', 'fr'];
    
    for (const lang of languages) {
      const langPages = records.filter(r => r.language === lang);
      if (langPages.length === 0) {
        console.warn(`⚠️ No pages found for language: ${lang}`);
      }
    }
    
    // Validate required fields
    for (const page of records) {
      if (!page.title || !page.page_id || !page.language) {
        console.error("❌ Page missing required fields");
        return false;
      }
    }
    
    // Check for home page
    const homePages = records.filter(r => r.page_id === 'home' || r.slug === '');
    if (homePages.length === 0) {
      console.warn("⚠️ No home page found");
    }
    
    logProgress("VALIDATION", `✅ Pages validation passed (${records.length} pages)`);
    return true;
    
  } catch (error) {
    console.error("❌ Pages validation failed:", error);
    return false;
  }
}

/**
 * Validate team members migration
 */
async function validateTeamMembers(pb: any): Promise<boolean> {
  try {
    const records = await pb.collection('team_members').getFullList();
    
    if (records.length === 0) {
      console.warn("⚠️ No team member records found");
      return true; // This is optional content
    }
    
    // Validate required fields
    for (const member of records) {
      if (!member.name || !member.member_id || !member.language) {
        console.error("❌ Team member missing required fields");
        return false;
      }
    }
    
    // Check for management team
    const managementMembers = records.filter(r => r.department === 'Management');
    if (managementMembers.length > 0) {
      logProgress("VALIDATION", `Found ${managementMembers.length} management team members`);
    }
    
    logProgress("VALIDATION", `✅ Team members validation passed (${records.length} members)`);
    return true;
    
  } catch (error) {
    console.error("❌ Team members validation failed:", error);
    return false;
  }
}

/**
 * Validate data integrity and relationships
 */
async function validateDataIntegrity(pb: any): Promise<boolean> {
  try {
    // Check for duplicate page IDs within same language
    const pages = await pb.collection('pages').getFullList();
    const pageGroups = new Map();
    
    for (const page of pages) {
      const key = `${page.language}-${page.page_id}`;
      if (pageGroups.has(key)) {
        console.error(`❌ Duplicate page ID found: ${page.page_id} (${page.language})`);
        return false;
      }
      pageGroups.set(key, page);
    }
    
    // Check for duplicate team member IDs within same language
    const teamMembers = await pb.collection('team_members').getFullList();
    const memberGroups = new Map();
    
    for (const member of teamMembers) {
      const key = `${member.language}-${member.member_id}`;
      if (memberGroups.has(key)) {
        console.error(`❌ Duplicate member ID found: ${member.member_id} (${member.language})`);
        return false;
      }
      memberGroups.set(key, member);
    }
    
    logProgress("VALIDATION", "✅ Data integrity validation passed");
    return true;
    
  } catch (error) {
    console.error("❌ Data integrity validation failed:", error);
    return false;
  }
}

/**
 * Generate validation report
 */
export async function generateValidationReport(): Promise<void> {
  const pb = await initializePocketBase();
  
  const collections = ['site_settings', 'navigation_menus', 'pages', 'team_members'];
  const report: any = {
    timestamp: new Date().toISOString(),
    collections: {}
  };
  
  for (const collection of collections) {
    try {
      const records = await pb.collection(collection).getFullList();
      report.collections[collection] = {
        count: records.length,
        sample: records.slice(0, 3).map(r => ({
          id: r.id,
          title: r.title || r.name || r.label || r.company_name,
          language: r.language || 'N/A'
        }))
      };
    } catch (error) {
      report.collections[collection] = {
        error: error.message
      };
    }
  }
  
  const reportPath = `validation-report-${new Date().toISOString().split('T')[0]}.json`;
  await Deno.writeTextFile(reportPath, JSON.stringify(report, null, 2));
  
  logProgress("VALIDATION", `Validation report saved: ${reportPath}`);
}

// Run validation if script is executed directly
if (import.meta.main) {
  try {
    const isValid = await validateMigration();
    await generateValidationReport();
    
    if (isValid) {
      console.log("✅ Migration validation completed successfully!");
    } else {
      console.error("❌ Migration validation failed!");
      Deno.exit(1);
    }
  } catch (error) {
    console.error("❌ Validation failed:", error);
    Deno.exit(1);
  }
}
