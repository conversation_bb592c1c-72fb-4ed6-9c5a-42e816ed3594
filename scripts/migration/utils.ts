import { <PERSON><PERSON><PERSON><PERSON>matter, ensureDir, basename, parseYaml, PocketBaseClient } from "./deps.ts";

/**
 * Parse Hugo markdown file with frontmatter
 */
export function parseFrontmatter(content: string): { frontmatter: <PERSON><PERSON>rontmatter, body: string } {
  // Try standard frontmatter with closing ---
  const standardRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
  let match = content.match(standardRegex);

  if (!match) {
    // Try frontmatter that ends with --- at the end of file (Hugo style)
    const hugoRegex = /^---\n([\s\S]*?)\n---\s*$/;
    match = content.match(hugoRegex);

    if (match) {
      // Frontmatter only, no body content
      try {
        const frontmatter = parseYaml(match[1]) as <PERSON><PERSON>rontmatter;
        return { frontmatter, body: '' };
      } catch (error) {
        console.warn("Failed to parse Hugo-style frontmatter:", error);
        return { frontmatter: {}, body: content };
      }
    }

    return { frontmatter: {}, body: content };
  }

  try {
    const frontmatter = parseYaml(match[1]) as <PERSON>Frontmatter;
    const body = match[2].trim();

    return { frontmatter, body };
  } catch (error) {
    console.warn("Failed to parse frontmatter:", error);
    return { frontmatter: {}, body: content };
  }
}

/**
 * Generate slug from file path
 */
export function generateSlug(filePath: string, _language: string): string {
  const fileName = basename(filePath).replace('.md', '');
  return fileName === '_index' ? '' : fileName;
}

/**
 * Generate page ID from slug
 */
export function generatePageId(slug: string): string {
  return slug || 'home';
}

/**
 * Generate member ID from name
 */
export function generateMemberId(name: string): string {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .substring(0, 50);
}

/**
 * Upload asset file to PocketBase
 */
export async function uploadAsset(_pb: unknown, assetPath: string): Promise<FormData> {
  const fullPath = `static/${assetPath}`;
  
  try {
    const fileData = await Deno.readFile(fullPath);
    const fileName = basename(assetPath);
    
    const formData = new FormData();
    formData.append('file', new Blob([fileData]), fileName);
    
    return formData;
  } catch (error) {
    console.warn(`Failed to read asset: ${fullPath}`, error);
    throw error;
  }
}

/**
 * Create backup directory with timestamp
 */
export async function createBackupDir(): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `backups/migration-${timestamp}`;
  
  await ensureDir(backupDir);
  return backupDir;
}

/**
 * Copy directory recursively
 */
export async function copyDirectory(source: string, destination: string): Promise<void> {
  const { copy } = await import("./deps.ts");
  await ensureDir(destination);
  await copy(source, destination, { overwrite: true });
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(): void {
  const required = ['POCKETBASE_URL', 'POCKETBASE_EMAIL', 'POCKETBASE_PASSWORD'];
  const missing = required.filter(env => !Deno.env.get(env));
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Initialize PocketBase client and authenticate
 */
export async function initializePocketBase(): Promise<PocketBaseClient> {
  const { PocketBase } = await import("./deps.ts");

  validateEnvironment();

  const pb = new PocketBase(Deno.env.get('POCKETBASE_URL'));

  try {
    // Try admin authentication first
    await pb.admins.authWithPassword(
      Deno.env.get('POCKETBASE_EMAIL')!,
      Deno.env.get('POCKETBASE_PASSWORD')!
    );
    console.log('✅ PocketBase authenticated as admin successfully');
  } catch (error) {
    console.warn('⚠️ Admin authentication failed, trying regular user auth:', error);
    // Fallback to regular user authentication
    await pb.collection('_superusers').authWithPassword(
      Deno.env.get('POCKETBASE_EMAIL')!,
      Deno.env.get('POCKETBASE_PASSWORD')!
    );
    console.log('✅ PocketBase authenticated as user successfully');
  }

  return pb;
}

/**
 * Log migration progress
 */
export function logProgress(step: string, message: string): void {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${step}: ${message}`);
}

/**
 * Handle migration errors
 */
export function handleError(context: string, error: unknown): void {
  console.error(`❌ Error in ${context}:`, error);
  if (error && typeof error === 'object' && 'response' in error) {
    const errorObj = error as { response: { data: unknown } };
    console.error('Response data:', errorObj.response.data);
  }
}
