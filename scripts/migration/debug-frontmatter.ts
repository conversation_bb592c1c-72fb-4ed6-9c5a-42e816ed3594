#!/usr/bin/env deno run --allow-read

import { parseFrontmatter } from "./utils.ts";

async function debugFrontmatter() {
  const aboutFile = "content/english/about.md";
  
  try {
    const content = await Deno.readTextFile(aboutFile);
    console.log("File content length:", content.length);
    
    const { frontmatter, body } = parseFrontmatter(content);
    
    console.log("\n=== FRONTMATTER KEYS ===");
    console.log(Object.keys(frontmatter));
    
    console.log("\n=== FRONTMATTER STRUCTURE ===");
    console.log(JSON.stringify(frontmatter, null, 2));
    
    console.log("\n=== LEADERSHIP SECTION ===");
    if (frontmatter.leadership) {
      console.log("Leadership found:", typeof frontmatter.leadership);
      console.log(JSON.stringify(frontmatter.leadership, null, 2));
    } else {
      console.log("No leadership section found");
    }
    
    console.log("\n=== BODY LENGTH ===");
    console.log("Body length:", body.length);
    console.log("Body preview:", body.substring(0, 100));
    
  } catch (error) {
    console.error("Error:", error);
  }
}

if (import.meta.main) {
  await debugFrontmatter();
}
