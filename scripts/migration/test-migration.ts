#!/usr/bin/env deno run --allow-read --allow-write --allow-net --allow-env

import { logProgress } from "./utils.ts";
import { validateContentStructure } from "./analyze-content.ts";

/**
 * Test migration setup and prerequisites
 */
async function testMigrationSetup(): Promise<boolean> {
  logProgress("TEST", "🧪 Testing migration setup...");
  
  let allTestsPassed = true;
  
  // Test 1: Check Deno permissions
  try {
    await Deno.readTextFile("deno.json").catch(() => {
      // File might not exist, that's okay
    });
    logProgress("TEST", "✅ Deno read permissions working");
  } catch (error) {
    console.error("❌ Deno read permissions failed:", error);
    allTestsPassed = false;
  }
  
  // Test 2: Check environment variables
  const requiredEnvVars = ['POCKETBASE_URL', 'POCKETBASE_EMAIL', 'POCKETBASE_PASSWORD'];
  for (const envVar of requiredEnvVars) {
    const value = Deno.env.get(envVar);
    if (!value) {
      console.error(`❌ Missing environment variable: ${envVar}`);
      allTestsPassed = false;
    } else {
      logProgress("TEST", `✅ Environment variable ${envVar} is set`);
    }
  }
  
  // Test 3: Check content structure
  try {
    const isValid = await validateContentStructure();
    if (isValid) {
      logProgress("TEST", "✅ Content structure validation passed");
    } else {
      console.error("❌ Content structure validation failed");
      allTestsPassed = false;
    }
  } catch (error) {
    console.error("❌ Content structure test failed:", error);
    allTestsPassed = false;
  }
  
  // Test 4: Check PocketBase connection
  try {
    const { initializePocketBase } = await import("./utils.ts");
    const pb = await initializePocketBase();
    
    // Test basic API call
    await pb.collection('site_settings').getFullList();
    logProgress("TEST", "✅ PocketBase connection successful");
  } catch (error) {
    console.error("❌ PocketBase connection failed:", error);
    console.error("   Make sure PocketBase is running and credentials are correct");
    allTestsPassed = false;
  }
  
  // Test 5: Check required collections exist
  try {
    const { initializePocketBase } = await import("./utils.ts");
    const pb = await initializePocketBase();
    
    const requiredCollections = ['site_settings', 'navigation_menus', 'pages', 'team_members'];
    
    for (const collection of requiredCollections) {
      try {
        await pb.collection(collection).getFullList();
        logProgress("TEST", `✅ Collection '${collection}' exists`);
      } catch (error) {
        console.error(`❌ Collection '${collection}' not found or inaccessible`);
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.error("❌ Collection validation failed:", error);
    allTestsPassed = false;
  }
  
  // Test 6: Check file system permissions
  try {
    const testDir = './test-migration-temp';
    await Deno.mkdir(testDir, { recursive: true });
    await Deno.writeTextFile(`${testDir}/test.txt`, 'test');
    await Deno.readTextFile(`${testDir}/test.txt`);
    await Deno.remove(testDir, { recursive: true });
    logProgress("TEST", "✅ File system permissions working");
  } catch (error) {
    console.error("❌ File system permissions test failed:", error);
    allTestsPassed = false;
  }
  
  return allTestsPassed;
}

/**
 * Test individual migration components
 */
async function testMigrationComponents(): Promise<boolean> {
  logProgress("TEST", "🔧 Testing migration components...");
  
  let allTestsPassed = true;
  
  // Test content analysis
  try {
    const { analyzeExistingContent } = await import("./analyze-content.ts");
    const inventory = await analyzeExistingContent();
    
    const totalContent = inventory.pages.english.length + inventory.pages.french.length;
    if (totalContent > 0) {
      logProgress("TEST", `✅ Content analysis found ${totalContent} pages`);
    } else {
      console.warn("⚠️ No content found - migration may not have much to migrate");
    }
  } catch (error) {
    console.error("❌ Content analysis test failed:", error);
    allTestsPassed = false;
  }
  
  // Test utility functions
  try {
    const { generateSlug, generatePageId, generateMemberId } = await import("./utils.ts");
    
    const testSlug = generateSlug("content/english/about.md", "english");
    const testPageId = generatePageId(testSlug);
    const testMemberId = generateMemberId("John Doe");
    
    if (testSlug && testPageId && testMemberId) {
      logProgress("TEST", "✅ Utility functions working");
    } else {
      console.error("❌ Utility functions test failed");
      allTestsPassed = false;
    }
  } catch (error) {
    console.error("❌ Utility functions test failed:", error);
    allTestsPassed = false;
  }
  
  return allTestsPassed;
}

/**
 * Main test runner
 */
async function runTests(): Promise<void> {
  console.log("🧪 Migration Test Suite");
  console.log("========================\n");
  
  const setupPassed = await testMigrationSetup();
  const componentsPassed = await testMigrationComponents();
  
  console.log("\n📊 Test Results:");
  console.log("================");
  
  if (setupPassed && componentsPassed) {
    console.log("✅ All tests passed! Migration is ready to run.");
    console.log("\nNext steps:");
    console.log("1. Run content analysis: deno run --allow-all analyze-content.ts");
    console.log("2. Run full migration: deno run --allow-all migrate-all.ts");
    console.log("3. Validate results: deno run --allow-all validate-migration.ts");
  } else {
    console.log("❌ Some tests failed. Please fix the issues before running migration.");
    
    if (!setupPassed) {
      console.log("\n🔧 Setup Issues:");
      console.log("- Check environment variables in .env file");
      console.log("- Ensure PocketBase server is running");
      console.log("- Verify content directory structure");
    }
    
    if (!componentsPassed) {
      console.log("\n🔧 Component Issues:");
      console.log("- Check migration script dependencies");
      console.log("- Verify content files exist and are readable");
    }
    
    Deno.exit(1);
  }
}

// Run tests
if (import.meta.main) {
  await runTests();
}
